{"name": "storybook", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "storybook dev -p 6006", "build": "storybook build", "start": "npx http-server storybook-static -p 3000", "lint": "eslint . --max-warnings 0"}, "dependencies": {"@svgr/webpack": "^8.1.0", "@workspace/ui": "workspace:*", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@newhighsco/storybook-addon-svgr": "^2.0.60", "@storybook/addon-essentials": "^8.0.0", "@storybook/addon-interactions": "^8.0.0", "@storybook/addon-links": "^8.0.0", "@storybook/addon-onboarding": "^8.0.0", "@storybook/blocks": "^8.0.0", "@storybook/manager-api": "^8.6.14", "@storybook/nextjs": "^9.0.1", "@storybook/react": "^8.0.0", "@storybook/react-vite": "^8.0.0", "@storybook/react-webpack5": "^8.6.14", "@storybook/test": "^8.0.0", "@tailwindcss/postcss": "^4.0.8", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.5.0", "@workspace/eslint-config": "workspace:^", "@workspace/typescript-config": "workspace:*", "storybook": "^8.0.0", "tailwindcss": "^4.0.8", "typescript": "^5.7.3", "vite": "^6.3.5", "vite-plugin-svgr": "^4.3.0"}}