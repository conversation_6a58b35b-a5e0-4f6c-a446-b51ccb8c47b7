# CaptchaInput Component Usage

The `CaptchaInput` component is used to render a CAPTCHA input field that supports different CAPTCHA methods (e.g., text CAPTCHA or Google reCAPTCHA) based on the active state.

## Example Usage

```tsx
<CaptchaInput
  useCaptchaWhichCaptchaIsActive={useCaptchaWhichCaptchaIsActive2}
  useCaptchaRequestTextCaptcha={useCaptchaRequestTextCaptcha2}
  onCaptchaChange={({ secretKey }) => {
    console.log("secretKey", secretKey);
  }}
  googleRecaptchaSiteKey={process.env.NEXT_PUBLIC_RECAPTCHA || ""}
/>
