import { useState } from "react";
import { Input, InputProps } from "@workspace/ui/components/input";
import Reload from "@/assets/icons/reload.svg";
import { Loading } from "@workspace/ui/components/loading";
import { ApiResultOfRequestCaptchaOutputDTO } from "@workspace/investment-api/auto-generated/models";

interface IInputCaptchaProps extends InputProps {
  captchaData?: ApiResultOfRequestCaptchaOutputDTO;
  onRefresh: () => void;
  onInputChange: (value: string) => void;
  isLoading?: boolean;
  error?: string;
  restProps?: InputProps;
}

export const InputCaptcha: React.FC<IInputCaptchaProps> = ({
  captchaData,
  onRefresh,
  onInputChange,
  isLoading,
  error,
  ...restProps
}) => {
  const [inputValue, setInputValue] = useState("");

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
    onInputChange(value);
  };

  return (
    <Input
      {...restProps}
      size="lg"
      value={inputValue}
      onChange={handleInputChange}
      helperText={error}
      aria-invalid={!!error}
      name="adminCaptchaInput"
      endAdornment={
        <div className="flex items-center gap-2 py-2">
          {captchaData?.data?.captchaImage && (
            <>
              <img
                src={captchaData.data.captchaImage}
                alt="CAPTCHA"
                className="h-9 min-w-[85px] rounded"
              />
            </>
          )}
          <div className="text-icon-neutral-secondary flex size-6 items-center justify-center">
            {isLoading ? (
              <Loading size="sm" />
            ) : (
              <Reload
                onClick={onRefresh}
                data-test="62711ac7-4ae7-47d2-bf02-0d570c1f4db3"
                className="text-icon-neutral-secondary size-6 cursor-pointer"
              />
            )}
          </div>
        </div>
      }
    />
  );
};
