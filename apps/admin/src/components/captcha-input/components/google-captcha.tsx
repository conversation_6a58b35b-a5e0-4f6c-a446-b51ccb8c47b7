import {
  useCallback,
  useEffect,
  useState,
  useRef,
  forwardRef,
  useImperativeHandle,
} from "react";
import { useGoogleReCaptcha } from "react-google-recaptcha-v3";

const TWO_MINS = 120000; // 2 minutes in milliseconds

// Define the ref interface
export interface GoogleCaptchaRef {
  refreshToken: () => Promise<void>;
  getCurrentToken: () => string | null;
  isTokenValid: () => boolean;
}

interface GoogleCaptchaProps {
  onCaptchaChange: (token: string | null) => void;
  onError?: (error: Error) => void;
  action?: string; // reCAPTCHA action name
  autoRefresh?: boolean; // Enable/disable auto refresh
}

export const GoogleCaptcha = forwardRef<GoogleCaptchaRef, GoogleCaptchaProps>(
  ({ onCaptchaChange, onError, action = "login", autoRefresh = true }, ref) => {
    const { executeRecaptcha } = useGoogleReCaptcha();
    const [currentToken, setCurrentToken] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const intervalRef = useRef<NodeJS.Timeout | null>(null);
    const tokenTimestampRef = useRef<number | null>(null);
    const mountedRef = useRef(true);

    // Clean up function to clear intervals and reset state
    const cleanup = useCallback(() => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }, []);

    // Check if current token is still valid (not older than 2 minutes)
    const isTokenValid = useCallback(() => {
      if (!currentToken || !tokenTimestampRef.current) return false;
      const now = Date.now();
      const tokenAge = now - tokenTimestampRef.current;
      return tokenAge < TWO_MINS;
    }, [currentToken]);

    // Execute reCAPTCHA and get new token
    const executeRecaptchaToken = useCallback(async (): Promise<
      string | null
    > => {
      if (!executeRecaptcha) {
        console.warn(
          "reCAPTCHA not initialized yet - waiting for Google script to load"
        );
        return null;
      }

      if (isLoading) {
        console.warn("reCAPTCHA execution already in progress");
        return currentToken;
      }

      setIsLoading(true);

      try {
        // Add a small delay to ensure reCAPTCHA is fully ready
        await new Promise((resolve) => setTimeout(resolve, 100));

        // Check if grecaptcha is available globally
        if (typeof window !== "undefined" && !(window as any).grecaptcha) {
          throw new Error("Google reCAPTCHA script not loaded");
        }

        const token = await executeRecaptcha(action);

        if (!token) {
          throw new Error("reCAPTCHA execution returned empty token");
        }

        // Only update state if component is still mounted
        if (mountedRef.current) {
          setCurrentToken(token);
          tokenTimestampRef.current = Date.now();
          onCaptchaChange(token);
        }

        return token;
      } catch (error) {
        console.error("reCAPTCHA execution failed:", error);
        const captchaError =
          error instanceof Error
            ? error
            : new Error("reCAPTCHA execution failed");

        if (mountedRef.current) {
          setCurrentToken(null);
          tokenTimestampRef.current = null;
          onCaptchaChange(null);
          onError?.(captchaError);
        }

        throw captchaError;
      } finally {
        if (mountedRef.current) {
          setIsLoading(false);
        }
      }
    }, [
      executeRecaptcha,
      action,
      onCaptchaChange,
      onError,
      isLoading,
      currentToken,
    ]);

    // Start automatic token refresh interval
    const startTokenRefresh = useCallback(() => {
      if (!autoRefresh) return;

      cleanup(); // Clear any existing interval

      intervalRef.current = setInterval(async () => {
        try {
          await executeRecaptchaToken();
        } catch (error) {
          console.error("Auto refresh failed:", error);
        }
      }, TWO_MINS);
    }, [autoRefresh, executeRecaptchaToken, cleanup]);

    // Manual refresh function
    const refreshToken = useCallback(async (): Promise<void> => {
      await executeRecaptchaToken();
    }, [executeRecaptchaToken]);

    // Expose methods through the ref
    useImperativeHandle(
      ref,
      () => ({
        refreshToken,
        getCurrentToken: () => currentToken,
        isTokenValid,
      }),
      [refreshToken, currentToken, isTokenValid]
    );

    // Initial token generation when component mounts or executeRecaptcha becomes available
    useEffect(() => {
      let timeoutId: NodeJS.Timeout;

      const initializeCaptcha = async () => {
        if (executeRecaptcha && !currentToken && !isLoading) {
          // Wait a bit longer for reCAPTCHA to be fully ready
          timeoutId = setTimeout(async () => {
            try {
              await executeRecaptchaToken();
            } catch (error) {
              console.error("Initial captcha generation failed:", error);
              // Retry after a longer delay
              setTimeout(() => {
                if (mountedRef.current && (executeRecaptcha as any)) {
                  executeRecaptchaToken().catch(console.error);
                }
              }, 2000);
            }
          }, 500);
        }
      };

      initializeCaptcha();

      return () => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
      };
    }, [executeRecaptcha, currentToken, isLoading, executeRecaptchaToken]);

    // Start auto-refresh when we have a valid token
    useEffect(() => {
      if (currentToken && autoRefresh) {
        startTokenRefresh();
      }

      return cleanup;
    }, [currentToken, autoRefresh, startTokenRefresh, cleanup]);

    // Cleanup on unmount
    useEffect(() => {
      mountedRef.current = true;

      return () => {
        mountedRef.current = false;
        cleanup();
      };
    }, [cleanup]);

    // Reset token when action changes
    useEffect(() => {
      if (currentToken) {
        executeRecaptchaToken();
      }
    }, [action]); // Only depend on action, not executeRecaptchaToken to avoid infinite loop

    return null; // This component doesn't render anything
  }
);

GoogleCaptcha.displayName = "GoogleCaptcha";
