"use client";

import { useAuthLogout2 } from "@workspace/investment-api/auto-generated/apis/auth/auth";
import { COOKIE_IS_LOGGED_IN } from "@/constants/cookies";
import { removeCookie } from "./useCookie";
import { useRouter } from "next/navigation";
import { ROUTE_LOGIN } from "@/constants/routes";

export const useLogout = () => {
  const router = useRouter();
  const { data, isLoading, isError, refetch } = useAuthLogout2({
    query: {
      enabled: false,
      retry: 0,
    },
  });

  const handleLogout = () => {
    refetch().then(() => {
      removeCookie(COOKIE_IS_LOGGED_IN);
      router.replace(ROUTE_LOGIN);
    });
  };

  return {
    logout: handleLogout,
    isLoading,
    isError,
    data,
  };
};
