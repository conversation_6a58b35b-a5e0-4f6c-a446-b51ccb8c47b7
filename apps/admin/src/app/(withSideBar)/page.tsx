"use client";

import { useLogout } from "@/hooks/useLogout";
import { Button } from "@workspace/ui/components/button";

export default function Page() {
  const { logout } = useLogout();
  return (
    <div className="flex items-center justify-center min-h-svh">
      <div className="flex flex-col items-center justify-center gap-4">
        <h1 className="text-2xl font-bold">صبا تامین</h1>
        <Button size="sm">Button</Button>

        <Button
          size="sm"
          color="destructive"
          onClick={() => {
            logout();
          }}
        >
          خروج
        </Button>
      </div>
    </div>
  );
}
