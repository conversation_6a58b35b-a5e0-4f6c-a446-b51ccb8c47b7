"use client";

interface ILayoutLoginProps {
  children: React.ReactNode;
}

export default function LayoutLogin({ children }: ILayoutLoginProps) {
  return (
    <div className=" h-full">
      <div className="flex flex-col justify-between gap-4  h-full">
        <div className="grid min-h-svh lg:grid-cols-2">
          <div className="flex flex-col gap-4 p-6 md:p-10">
            <div className="flex flex-1 items-center justify-center">
              <div className="w-[450px]">{children}</div>
            </div>
          </div>
          <div className="relative hidden lg:block">
            <img
              src="/images/loginImage.png"
              alt="Image"
              className="absolute inset-0 h-full w-full object-cover"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
