import LoginForm from "./(components)/loginForm";
import { useLoginHook } from "@/app/login/useLoginHook";

export function Login() {
  const { handleLogin } = useLoginHook();

  return <LoginForm handleSubmit={handleLogin} />;
}

export function LoginLoading() {
  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900"></div>
    </div>
  );
}
