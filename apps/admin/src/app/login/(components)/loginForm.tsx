import SabaLogo from "@/assets/icons/saba-login-logo.svg";
import { CaptchaInput } from "@/components/captcha-input";
import { CaptchaInputRef } from "@/components/captcha-input/captcha-input";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  useCaptchaRequestTextCaptcha2,
  useCaptchaWhichCaptchaIsActive2,
} from "@workspace/investment-api/auto-generated/apis/captcha/captcha";
import { Button } from "@workspace/ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from "@workspace/ui/components/form";
import { Input } from "@workspace/ui/components/input";
import { InputPassword } from "@workspace/ui/components/input-password";
import { Loading } from "@workspace/ui/components/loading";
import { useRef } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const formSchema = z.object({
  password: z.string().nonempty("این فیلد اجباری است"),
  username: z.string().nonempty("این فیلد اجباری است"),
  secretKey: z.string().nonempty("این فیلد اجباری است"),
});
export type LoginFormValues = z.infer<typeof formSchema> & {
  secretKey: string;
};

export type LoginFormHandleSubmitFunction = (
  values: LoginFormValues
) => Promise<unknown>;

interface LoginFormProps {
  handleSubmit: LoginFormHandleSubmitFunction;
  secretKey?: string;
}

function LoginForm({ handleSubmit }: LoginFormProps) {
  const captchaInputRef = useRef<CaptchaInputRef>(null);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: "",
      username: "",
      secretKey: "",
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    captchaInputRef.current?.refreshCaptcha();
    return handleSubmit(values);
  }

  return (
    <>
      <div className="flex flex-col justify-between gap-6">
        <SabaLogo className="text-icon-neutral-default mx-auto h-[76px] w-[288px]" />

        <div className="font-bold">ورود به داشبورد مدیریتی</div>

        <div className="text-sm font-light">
          بــــرای ورود به حساب کاربری خود لـــطفا نام کاربری و رمز عبور خود را
          وارد کنید.
        </div>
        <Form {...form}>
          <form
            id="login-form"
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col justify-between pt-3"
          >
            <div className="flex flex-col gap-4 justify-between">
              <FormField
                control={form.control}
                name="username"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        size="lg"
                        title="نام کاربری"
                        {...field}
                        helperText={fieldState?.error?.message}
                        aria-invalid={!!fieldState?.error?.message}
                        name="username"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormControl>
                      <InputPassword
                        size="lg"
                        title="رمز عبور"
                        {...field}
                        helperText={fieldState?.error?.message}
                        aria-invalid={!!fieldState?.error?.message}
                        name="password"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="secretKey"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormControl>
                      <CaptchaInput
                        useCaptchaWhichCaptchaIsActive={
                          useCaptchaWhichCaptchaIsActive2
                        }
                        useCaptchaRequestTextCaptcha={
                          useCaptchaRequestTextCaptcha2
                        }
                        onCaptchaChange={({ secretKey }) => {
                          field.onChange(secretKey);
                        }}
                        googleRecaptchaSiteKey={
                          process.env.NEXT_PUBLIC_RECAPTCHA || ""
                        }
                        inputProps={{
                          title: "کد  مقابل را وارد کنید",
                          placeholder: " ",
                          helperText: fieldState?.error?.message,
                          "aria-invalid": !!fieldState?.error?.message,
                        }}
                        ref={captchaInputRef}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
      </div>

      <Button
        className="w-full mt-4"
        variant="fill"
        size="lg"
        type="submit"
        form="login-form"
        disabled={form.formState.isSubmitting}
        endAdornment={form.formState.isSubmitting && <Loading size="sm" />}
        data-test="c635cdf0-41e2-4778-a78a-537fe514c8b5"
      >
        ورود
      </Button>
    </>
  );
}
export default LoginForm;
