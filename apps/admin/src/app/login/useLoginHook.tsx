import { LoginFormValues } from "@/app/login/(components)/loginForm";
import { COOKIE_IS_LOGGED_IN } from "@/constants/cookies";
import { ROUTE_HOME } from "@/constants/routes";
import { setCookie } from "@/hooks/useCookie";
import { useAuthLogin } from "@workspace/investment-api/auto-generated/apis/auth/auth";
import { toast } from "@workspace/ui/components/toast";
import { useRouter } from "next/navigation";
import { useState } from "react";

export const useLoginHook = () => {
  const router = useRouter();
  const [secretKey, setSecretKey] = useState("");

  const { mutateAsync: authLogin } = useAuthLogin({
    request: {
      headers: {
        "captcha-code": secretKey,
        "Content-Type": "multipart/form-data",
      },
    },
  });

  const handleLogin = async (value: LoginFormValues) => {
    setSecretKey(value?.secretKey);

    return authLogin({
      data: { password: value?.password, username: value?.username },
    })
      .then(() => {
        router.replace(ROUTE_HOME);
        setCookie(COOKIE_IS_LOGGED_IN, "true");
      })
      .catch((err) => {
        toast.error(err.response?.data?.errorMessage);
      });
  };
  return { handleLogin };
};
