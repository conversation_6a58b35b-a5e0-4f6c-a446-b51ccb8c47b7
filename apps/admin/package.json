{"name": "admin", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack -p 3001", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@svgr/webpack": "^8.1.0", "@workspace/investment-api": "workspace:*", "@workspace/ui": "workspace:*", "js-cookie": "^3.0.5", "lucide-react": "^0.475.0", "next": "^15.2.3", "next-themes": "^0.4.4", "path-to-regexp": "^6.2.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-google-recaptcha-v3": "^1.11.0", "react-hook-form": "^7.57.0", "zod": "^3.25.67"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@workspace/eslint-config": "workspace:^", "@workspace/typescript-config": "workspace:*", "typescript": "^5.7.3"}}