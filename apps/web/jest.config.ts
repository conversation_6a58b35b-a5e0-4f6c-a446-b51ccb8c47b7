import type { Config } from "jest";
import nextJest from "next/jest.js";

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files in your test environment
  dir: "./",
});

// Add any custom config to be passed to Jest
const config: Config = {
  coverageProvider: "v8",
  testEnvironment: "jsdom",
  // Add more setup options before each test is run
  // setupFilesAfterEnv: ['<rootDir>/jest.setup.ts'],

  // preset: "ts-jest",
  moduleFileExtensions: ["js", "jsx", "ts", "tsx", "json", "node"],
  transform: {
    "^.+\\.(t|j)sx?$": [
      "ts-jest",
      {
        tsconfig: "tsconfig.json",
      },
    ],
  },
  moduleNameMapper: {
    "^@workspace/ui/assets/icons/(.*)$": "<rootDir>/src/__mocks__/svg.js",
    "^@/assets/icons/(.*)$": "<rootDir>/src/__mocks__/svg.js",
    "^@workspace/ui/(.*)$": "<rootDir>/../../packages/ui/src/$1",
    "^@workspace/investment-api/(.*)$":
      "<rootDir>/../../packages/investment-api/src/$1",
    "^@/(.*)$": "<rootDir>/src/$1",
    "\\.(css|less|scss|sass)$": "identity-obj-proxy",
    "\\.svg": "<rootDir>/src/__mocks__/svg.js",
  },
  setupFilesAfterEnv: ["<rootDir>/jest.setup.ts"],
  testPathIgnorePatterns: ["/node_modules/", "/dist/"],
  transformIgnorePatterns: [
    "/node_modules/(?!(@radix-ui|class-variance-authority|tailwind-merge)/)",
  ],
  coverageDirectory: "coverage",
  verbose: true,
  testEnvironmentOptions: {
    customExportConditions: ["node", "node-addons"],
  },
};

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
// @ts-ignore
module.exports = createJestConfig(config);
