import { COOKIE_IS_LOGGED_IN } from "@/constants/cookies";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { pathToRegexp } from "path-to-regexp";
import { ROUTE_HOME, ROUTE_LOGIN } from "./constants/routes";

export const config = {
  matcher: [
    "/((?!api|_next/static|_next/image|favicon.ico|manifest.json|assets).*)",
  ],
};

const authRequiredPaths = [
  "/((?!api|_next/static|_next/image|favicon.ico|manifest.json|assets|images|hc).*)",
]; // means all paths except /api, /_next/static, /_next/image, /favicon.ico, /assets, /images
const authRequiredPathsWhiteList = [ROUTE_LOGIN];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  const isAuthRequiredPath =
    pathToRegexp(authRequiredPaths).test(pathname || "") &&
    !pathToRegexp(authRequiredPathsWhiteList).test(pathname || "");

  const isLoggedIn = request.cookies.get(COOKIE_IS_LOGGED_IN)?.value;

  const loginPath = ROUTE_LOGIN;

  if (isAuthRequiredPath && !isLoggedIn) {
    return NextResponse.redirect(new URL(loginPath, request.url));
  }

  if (isLoggedIn && pathToRegexp([loginPath]).test(pathname || "")) {
    return NextResponse.redirect(new URL(ROUTE_HOME, request.url));
  }

  return NextResponse.next();
}
