import React, { useState, useEffect } from "react";
import { setCookie, getCookie } from "@/hooks/useCookie";
import Close from "@/assets/icons/Close.svg";
import NewTab from "@/assets/icons/new-tab.svg";
import { Button } from "@workspace/ui/components/button";
import { HIDE_SEJAM_ALERT } from "@/constants/cookies";
import { useProfileGetLoggedInUserProfile } from "@workspace/investment-api/auto-generated/apis/profile/profile";

const SejamAlert = () => {
  const { data, isLoading } = useProfileGetLoggedInUserProfile();
  const [shouldHideAlert, setShouldHideAlert] = useState(false);
  const isVisitor = !!data?.data?.visitorUserProfile?.visitorUserId;

  const handleShowAlert = () => {
    setShouldHideAlert(true);
    setCookie(HIDE_SEJAM_ALERT, "true");
  };

  useEffect(() => {
    const cookieValue = getCookie(HIDE_SEJAM_ALERT);
    setShouldHideAlert(!!cookieValue);
  }, []);

  if (isLoading) {
    return null;
  }

  if (typeof window === "undefined") return null;

  if (shouldHideAlert || !isVisitor) return null;

  return (
    <div className="bg-surface-nautral-default-3 flex flex-col gap-3.5 rounded-br-2xl rounded-bl-2xl px-4 py-5">
      <div className="flex items-start justify-between gap-7">
        <span className="text-text-nautral-default text-xs leading-5">
          برای شروع فرایند سرمایه‌گذاری، ابتدا در سامانه سجام احراز هویت کنید.
        </span>
        <Close
          className="text-icon-neutral-default h-5 w-8 cursor-pointer"
          onClick={handleShowAlert}
        />
      </div>

      <Button
        color="default"
        size="xs"
        className="w-[153px] rounded-xl"
        startAdornment={
          <NewTab className="text-icon-on-surface-default size-4" />
        }
      >
        احراز هویت (سجامی)
      </Button>
    </div>
  );
};

export default SejamAlert;
