"use client";
import { SejamAlert } from "@/app/(home)/components/sejam-alert";
import { useLogout } from "@/hooks/useLogout";
import { Button } from "@workspace/ui/components/button";
import type { FC } from "react";

const Page: FC = () => {
  const { logout } = useLogout();

  return (
    <div className="flex min-h-svh flex-col">
      <SejamAlert />

      <Button size="sm" color="destructive" onClick={logout}>
        خروج
      </Button>
    </div>
  );
};

export default Page;
