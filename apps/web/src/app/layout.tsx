import localFont from "next/font/local";
import { Providers } from "@/components/providers";
import { Viewport } from "next";
import "../styles/global.css";

const yekan = localFont({
  src: [
    {
      path: "../assets/fonts/YekanBakhFaNum-Thin.woff",
      weight: "100",
      style: "normal",
    },
    {
      path: "../assets/fonts/YekanBakhFaNum-Light.woff",
      weight: "300",
      style: "normal",
    },
    {
      path: "../assets/fonts/YekanBakhFaNum-Regular.woff",
      weight: "400",
      style: "normal",
    },
    {
      path: "../assets/fonts/YekanBakhFaNum-SemiBold.woff",
      weight: "600",
      style: "normal",
    },
    {
      path: "../assets/fonts/YekanBakhFaNum-Bold.woff",
      weight: "700",
      style: "normal",
    },
    {
      path: "../assets/fonts/YekanBakhFaNum-ExtraBold.woff",
      weight: "800",
      style: "normal",
    },
    {
      path: "../assets/fonts/YekanBakhFaNum-Black.woff",
      weight: "900",
      style: "normal",
    },
    {
      path: "../assets/fonts/YekanBakhFaNum-ExtraBlack.woff",
      weight: "950",
      style: "normal",
    },
  ],
  variable: "--font-yekan",
});

export const viewport: Viewport = {
  initialScale: 1,
  width: "device-width",
  maximumScale: 1,
  userScalable: false,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html
      lang="fa"
      suppressHydrationWarning
      dir="rtl"
      className="container mx-auto h-full max-w-2xl"
    >
      <body
        className={`${yekan.variable} font-yekan relative h-full antialiased`}
      >
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
