"use client";

import MobileLayout from "@/app/login/components/layout/mobile/mobile-login-layout";
import { ResponsiveRenderer } from "@workspace/ui/components/responsive-renderer";

interface ILayoutProps {
  children: React.ReactNode;
}

export default function Layout({ children }: ILayoutProps) {
  return (
    <ResponsiveRenderer
      desktop={<MobileLayout>{children}</MobileLayout>}
      mobile={<MobileLayout>{children}</MobileLayout>}
    />
  );
}
