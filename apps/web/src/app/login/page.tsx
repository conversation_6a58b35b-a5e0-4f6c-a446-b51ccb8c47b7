"use client";

import { useState, Suspense } from "react";
import {
  useAuthGetLoginMethod,
  useAuthLoginByOTP,
  useAuthLoginByPassword,
} from "@workspace/investment-api/auto-generated/apis/auth/auth";
import { toast } from "@workspace/ui/components/toast";
import { useRouter } from "next/navigation";
import { ROUTE_HOME } from "@/constants/routes";
import { COOKIE_IS_LOGGED_IN } from "@/constants/cookies";
import { setCookie } from "@/hooks/useCookie";
import {
  FormValues,
  SetErrorLogin,
} from "@/app/login/components/login/mobile/mobile-login";
import LoginByPassword, {
  LoginByPasswordFormValues,
} from "@/app/login/components/login-by-password/login-by-password";
import {
  LoginByOtpFormValues,
  SetErrorLoginByOtp,
} from "@/app/login/components/login-by-otp/mobile/mobile-login-by-otp";
import LoginByOtp from "@/app/login/components/login-by-otp/login-by-otp";
import Login from "@/app/login/components/login/login";
import { useLoginQueryStates } from "@/app/login/hooks/useLoginQueryStates";
import { CaptchaTypeEnum } from "@/components/captcha-input/types";
import {
  THREE_TIMES_TRY_ERROR,
  WRONG_CODE_MESSAGE,
  WRONG_OTP_ERROR,
} from "@/app/login/utils";

function LoginLoading() {
  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900"></div>
    </div>
  );
}

function LoginContent() {
  const router = useRouter();
  const [mobile, setMobile] = useState("");
  const [secretKey, setSecretKey] = useState("");
  const [captchaKey, setCaptchaKey] = useState("");
  const [captchaCode, setCaptchaCode] = useState("");

  const [queryStates, setQueryStates] = useLoginQueryStates();

  const loginType = queryStates?.loginType;

  const errorTile = (errorCode?: string) =>
    errorCode && THREE_TIMES_TRY_ERROR.includes(errorCode)
      ? "حساب شما مسدود است!"
      : " ";

  const { mutateAsync: loginByOtp } = useAuthLoginByOTP();
  const { mutateAsync: loginByPassword } = useAuthLoginByPassword();
  const [captchaType, setCaptchaType] = useState<CaptchaTypeEnum | null>(null);

  const loginMethodHeaders =
    captchaType === CaptchaTypeEnum.GoogleRecaptchaV3
      ? { "captcha-code": captchaKey || secretKey }
      : {
          "captcha-code": captchaCode,
          "captcha-secret-key": captchaKey || secretKey,
        };

  const { refetch } = useAuthGetLoginMethod(mobile, {
    query: {
      enabled: false,
      retry: 0,
    },
    request: {
      headers: loginMethodHeaders,
    },
  });

  /* ------------------------------ handle submit ----------------------------- */
  const handleSubmit = async (
    value: FormValues,
    { setError }: { setError: SetErrorLogin },
  ) => {
    setMobile(value?.mobile);
    setCaptchaKey(value?.secretKey);
    setCaptchaCode(value?.captchaCode);

    return new Promise((resolve, reject) => {
      return setTimeout(async () => {
        return await refetch()
          .then((data) => {
            if (data?.data?.data) {
              setQueryStates({ loginType: data?.data?.data?.grantType });
              setSecretKey(data?.data?.data?.secretKey || "");
              resolve(data);
            } else {
              if ((data as any)?.error?.response?.data?.errorMessage) {
                toast.error(
                  errorTile((data as any)?.error?.response?.data?.errorCode),
                  {
                    description: (data as any)?.error?.response?.data
                      ?.errorMessage,
                  },
                );
              }
              reject();
              setError("mobile", {
                message: (data as any)?.error?.response?.data?.errorMessage,
              });
            }
          })
          .catch((err) => {
            reject();
            if (err.response?.data?.errorMessage) {
              toast.error(errorTile(err?.response?.data?.errorCode), {
                description: err.response?.data?.errorMessage,
              });
            }
            setError("mobile", { message: err.response?.data?.errorMessage });
          });
      }, 0);
    });
  };

  /* ------------------------- handle submit password ------------------------- */
  const handleSubmitPassword = async (value: LoginByPasswordFormValues) => {
    return loginByPassword({
      data: { password: value?.password, secretKey: secretKey },
    })
      .then(() => {
        router.replace(ROUTE_HOME);
        setCookie(COOKIE_IS_LOGGED_IN, "true");
      })
      .catch((err) => {
        if (err.response?.data?.errorMessage) {
          toast.error(errorTile(err?.response?.data?.errorCode), {
            description: err.response?.data?.errorMessage,
          });
        }
      });
  };

  /* ---------------------------- handle submit otp --------------------------- */
  const handleSubmitOtp = async (
    value: LoginByOtpFormValues,
    { setError }: { setError: SetErrorLoginByOtp },
  ) => {
    return loginByOtp({
      data: { otp: value?.otp, secretKey: secretKey },
    })
      .then(() => {
        router.replace(ROUTE_HOME);
        setCookie(COOKIE_IS_LOGGED_IN, "true");
      })
      .catch((err) => {
        if (err.response?.data?.errorCode === WRONG_OTP_ERROR) {
          setError("otp", { message: WRONG_CODE_MESSAGE });

          toast.error("", { description: err.response?.data?.errorMessage });
        } else if (err.response?.data?.errorMessage) {
          toast.error(errorTile(err?.response?.data?.errorCode), {
            description: err.response?.data?.errorMessage,
          });
        }
      });
  };

  if (loginType === 100) {
    return (
      <LoginByPassword
        secretKey={secretKey}
        mobile={mobile}
        handleSubmitPassword={handleSubmitPassword}
      />
    );
  }

  if (loginType === 200) {
    return (
      <LoginByOtp
        secretKey={secretKey}
        mobile={mobile}
        handleSubmitOtp={handleSubmitOtp}
      />
    );
  }

  return (
    <Login
      handleSubmit={handleSubmit}
      captchaType={captchaType}
      setCaptchaType={setCaptchaType}
    />
  );
}

export default function Page() {
  return (
    <Suspense fallback={<LoginLoading />}>
      <LoginContent />
    </Suspense>
  );
}
