import { useQueryStates, parseAsInteger } from "nuqs";

type LoginQueryStates = {
  loginType: number;
};

type SetLoginQueryStates = (
  values:
    | Partial<LoginQueryStates>
    | ((prev: LoginQueryStates) => Partial<LoginQueryStates>),
  options?: {
    scroll?: boolean;
    shallow?: boolean;
    history?: "push" | "replace";
  },
) => Promise<URLSearchParams>;

export const useLoginQueryStates = (): [
  LoginQueryStates,
  SetLoginQueryStates,
] => {
  const [queryStates, setQueryStates] = useQueryStates(
    {
      loginType: parseAsInteger.withDefault(0),
    },
    { shallow: false, history: "push" },
  );

  return [queryStates, setQueryStates];
};
