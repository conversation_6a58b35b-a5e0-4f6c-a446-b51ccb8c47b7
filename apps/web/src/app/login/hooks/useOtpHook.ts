import { useLoginQueryStates } from "@/app/login/hooks/useLoginQueryStates";
import { ROUTE_LOGIN } from "@/constants/routes";
import { useAuthSendLoginOTP } from "@workspace/investment-api/auto-generated/apis/auth/auth";
import { toast } from "@workspace/ui/components/toast";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface IOtpHookProps {
  secretKey?: string;
}

export const useOtpHook = ({ secretKey }: IOtpHookProps) => {
  const router = useRouter();
  const [otpSendDate, setOtpSendDate] = useState<string>("");
  const [otpExpirationDate, setOtpExpirationDate] = useState<string>("");

  const [queryStates, setQueryStates] = useLoginQueryStates();

  const { mutate: sendLoginOtpMutate, isPending: isLoadingSendOtp } =
    useAuthSendLoginOTP({ mutation: { retry: 0 } });

  const onSendOtp = () => {
    sendLoginOtpMutate(
      { data: { secretKey } },
      {
        onSuccess: (data) => {
          if (data?.data?.otpSendDate) setOtpSendDate(data?.data?.otpSendDate);
          if (data?.data?.otpExpirationDate)
            setOtpExpirationDate(data?.data?.otpExpirationDate);
        },
        onError: (error: any) => {
          if (
            error.response?.data?.errorCode ===
            "692593F7-C8D5-448B-913A-EDB51A6F1129"
          ) {
            router.replace(ROUTE_LOGIN);
            setQueryStates({ loginType: 0 });
          }

          if (error.response?.data?.errorMessage) {
            toast.error("", {
              description: error.response?.data?.errorMessage,
            });
          }
        },
      },
    );
  };

  return { onSendOtp, isLoadingSendOtp, otpSendDate, otpExpirationDate };
};
