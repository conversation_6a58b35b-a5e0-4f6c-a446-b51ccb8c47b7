import React from "react";

import { ResponsiveRenderer } from "@workspace/ui/components/responsive-renderer";
import MobileLogin, {
  MobileLoginProps,
} from "@/app/login/components/login/mobile/mobile-login";

interface ILoginByOtpProps extends MobileLoginProps {}

function Login(props: ILoginByOtpProps) {
  return (
    <ResponsiveRenderer
      desktop={<MobileLogin {...props} />}
      mobile={<MobileLogin {...props} />}
    />
  );
}

export default Login;
