import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import "@testing-library/jest-dom";
import { render, screen } from "@testing-library/react";
import MobileLogin from "./mobile-login";

// Mock API responses
jest.mock("@workspace/investment-api/apiService", () => ({
  __esModule: true,
  default: {
    login: jest.fn().mockResolvedValue({ success: true }),
  },
}));

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
);

const mockHandleSubmit = jest.fn().mockResolvedValue({});

describe("MobileLogin", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the login form", () => {
    render(<MobileLogin handleSubmit={mockHandleSubmit} />, { wrapper });

    expect(screen.getByText("ورود | ثبت نام")).toBeInTheDocument();
  });
});
