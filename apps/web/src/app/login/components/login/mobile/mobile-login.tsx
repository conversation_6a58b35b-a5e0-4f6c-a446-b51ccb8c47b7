import { CaptchaInput } from "@/components/captcha-input";
import { CaptchaInputRef } from "@/components/captcha-input/captcha-input";
import { CaptchaTypeEnum } from "@/components/captcha-input/types";
import { SplashLoading } from "@/components/splash-loading";
import { IRANIAN_MOBILE_PREFIX_REGEX } from "@/constants/regexes";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  useCaptchaRequestTextCaptcha,
  useCaptchaWhichCaptchaIsActive,
} from "@workspace/investment-api/auto-generated/apis/captcha/captcha";
import { Button } from "@workspace/ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from "@workspace/ui/components/form";
import { InputNumber } from "@workspace/ui/components/input-number";
import { Loading } from "@workspace/ui/components/loading";
import { useEffect, useRef, useState } from "react";
import { useForm, UseFormSetError } from "react-hook-form";
import { z } from "zod";

const formSchema = z.object({
  mobile: z
    .string()
    .nonempty("شماره موبایل را وارد کنید.")
    .regex(IRANIAN_MOBILE_PREFIX_REGEX, "شماره وارد شده صحیح نیست!"),
  secretKey: z.string().nonempty("کد را وارد کنید"),
  captchaCode: z.string(),
});
export type FormValues = z.infer<typeof formSchema>;

export type SetErrorLogin = UseFormSetError<z.infer<typeof formSchema>>;

export type HandleSubmitFunction = (
  values: FormValues,
  { setError }: { setError: SetErrorLogin },
) => Promise<unknown>;

export interface MobileLoginProps {
  handleSubmit: HandleSubmitFunction;
  captchaType: CaptchaTypeEnum | null;
  setCaptchaType: (value: CaptchaTypeEnum | null) => void;
}

function MobileLogin({
  handleSubmit,
  captchaType,
  setCaptchaType,
}: MobileLoginProps) {
  const [errorMessage, setErrorMessage] = useState("");
  const captchaInputRef = useRef<CaptchaInputRef>(null);
  const inputRef = useRef<any>(null);

  useEffect(() => {
    setTimeout(() => {
      inputRef.current?.focus();
    }, 300);
  }, []);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      mobile: "",
      secretKey: "",
      captchaCode: "",
    },
    mode: "onChange",
  });

  const secretKey = form.watch("secretKey");

  const [isLoading, setIsLoading] = useState(
    captchaType === CaptchaTypeEnum.GoogleRecaptchaV3 ? !secretKey : true,
  );

  function onSubmit(values: z.infer<typeof formSchema>) {
    captchaInputRef.current?.refreshCaptcha();
    return handleSubmit(values, {
      setError: () => captchaInputRef.current?.setInputValue?.(""),
    });
  }

  return (
    <div className="mt-[112px] flex flex-1 flex-col justify-between">
      <div className="flex flex-col justify-between gap-9">
        <div>
          <p className="text-text-nautral-default text-lg font-bold">
            ورود | ثبت نام
          </p>
          <p className="text-text-nautral-secondary mt-5 text-xs">
            لطفا شماره موبایل خود را وارد کنید.
          </p>
        </div>

        <Form {...form}>
          <form
            id="login-form"
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col justify-between"
          >
            {isLoading && <SplashLoading />}
            <div className="flex flex-col gap-4">
              <FormField
                control={form.control}
                name="mobile"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormControl>
                      <InputNumber
                        size="lg"
                        commaSeparated={false}
                        title="شماره موبایل"
                        maxLength={11}
                        {...field}
                        ref={inputRef}
                        autoFocus
                        helperText={fieldState?.error?.message || errorMessage}
                        aria-invalid={
                          !!fieldState?.error?.message || !!errorMessage
                        }
                        data-test="19463b53-71c7-4f65-90a6-3f1ab9f255d7"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="secretKey"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormControl>
                      <CaptchaInput
                        useCaptchaWhichCaptchaIsActive={
                          useCaptchaWhichCaptchaIsActive
                        }
                        useCaptchaRequestTextCaptcha={
                          useCaptchaRequestTextCaptcha
                        }
                        onCaptchaChange={({ secretKey, captchaCode }) => {
                          field.onChange(secretKey);
                          form.setValue("captchaCode", captchaCode || "");
                        }}
                        googleRecaptchaSiteKey={
                          process.env.NEXT_PUBLIC_RECAPTCHA || ""
                        }
                        inputProps={{
                          title: "کد  مقابل را وارد کنید",
                          placeholder: " ",
                          helperText: fieldState?.error?.message,
                          "aria-invalid": !!fieldState?.error?.message,
                        }}
                        setIsLoading={setIsLoading}
                        setErrorMessage={setErrorMessage}
                        ref={captchaInputRef}
                        setCaptchaType={setCaptchaType}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
      </div>

      <Button
        variant="fill"
        size="md"
        type="submit"
        form="login-form"
        disabled={form.formState.isSubmitting || isLoading}
        data-test="b8c3093c-0d1d-4ac1-97a3-5289178edfba"
        endAdornment={
          (form.formState.isSubmitting || isLoading) && <Loading size="sm" />
        }
      >
        تایید و ادامه
      </Button>
    </div>
  );
}
export default MobileLogin;
