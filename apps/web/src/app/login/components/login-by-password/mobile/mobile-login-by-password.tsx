import BackIcon from "@/assets/icons/arrow-right.svg";
import { ROUTE_LOGIN } from "@/constants/routes";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@workspace/ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from "@workspace/ui/components/form";
import { InputPassword } from "@workspace/ui/components/input-password";
import { Loading } from "@workspace/ui/components/loading";
import Link from "next/link";
import { useForm } from "react-hook-form";
import { z } from "zod";

const formSchema = z.object({
  password: z.string().nonempty("رمز عبور را وارد کنید."),
});
export type LoginByPasswordFormValues = z.infer<typeof formSchema> & {
  secretKey: string;
};

export type LoginByPasswordHandleSubmitFunction = (
  values: LoginByPasswordFormValues,
) => Promise<unknown>;

interface LoginByPasswordProps {
  handleSubmit: LoginByPasswordHandleSubmitFunction;
  secretKey?: string;
  mobile?: string;
}

function MobileLoginByPassword({
  handleSubmit,
  secretKey,
}: LoginByPasswordProps) {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: "",
    },
  });

  function onSubmit(values: z.infer<typeof formSchema>) {
    return handleSubmit({ ...values, secretKey: secretKey || "" });
  }

  return (
    <div className="mt-11.5 flex flex-1 flex-col justify-between">
      <div className="flex flex-col justify-between gap-6">
        <div>
          <Link
            className="flex items-center gap-1"
            href={ROUTE_LOGIN}
            data-test="08da8a8d-7b11-4c7e-80f2-fd15a4dd34bd"
          >
            <BackIcon className="text-text-nautral-default size-4" />
            <span className="text-text-nautral-default text-xs">بازگشت</span>
          </Link>

          <p className="text-text-nautral-default mt-12 text-lg font-bold">
            رمز عبور خود را وارد کنید.
          </p>
        </div>

        <Form {...form}>
          <form
            id="login-form"
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col justify-between"
          >
            <div className="flex flex-col justify-between">
              <FormField
                control={form.control}
                name="password"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormControl>
                      <InputPassword
                        size="lg"
                        title="رمز عبور"
                        {...field}
                        helperText={fieldState?.error?.message}
                        aria-invalid={!!fieldState?.error?.message}
                        data-test="0aa51f21-0928-4e84-9527-0f0ea424e625"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
      </div>

      <Button
        variant="fill"
        size="md"
        type="submit"
        form="login-form"
        disabled={form.formState.isSubmitting}
        endAdornment={form.formState.isSubmitting && <Loading size="sm" />}
        data-test="8c97c56e-fa65-4aff-83ed-0ccfed41778e"
      >
        ورود
      </Button>
    </div>
  );
}
export default MobileLoginByPassword;
