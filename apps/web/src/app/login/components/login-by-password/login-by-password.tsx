import { z } from "zod";
import { ResponsiveRenderer } from "@workspace/ui/components/responsive-renderer";
import MobileLoginByPassword from "@/app/login/components/login-by-password/mobile/mobile-login-by-password";

const formSchema = z.object({
  password: z.string().nonempty("این فیلد اجباری است"),
});
export type LoginByPasswordFormValues = z.infer<typeof formSchema> & {
  secretKey: string;
};

export type LoginByPasswordHandleSubmitFunction = (
  values: LoginByPasswordFormValues,
) => Promise<unknown>;

interface LoginByPasswordProps {
  handleSubmitPassword: LoginByPasswordHandleSubmitFunction;
  secretKey?: string;
  mobile?: string;
}

function LoginByPassword({
  handleSubmitPassword,
  secretKey,
  mobile,
}: LoginByPasswordProps) {
  return (
    <ResponsiveRenderer
      desktop={
        <MobileLoginByPassword
          handleSubmit={handleSubmitPassword}
          mobile={mobile}
          secretKey={secretKey}
        />
      }
      mobile={
        <MobileLoginByPassword
          handleSubmit={handleSubmitPassword}
          mobile={mobile}
          secretKey={secretKey}
        />
      }
    />
  );
}
export default LoginByPassword;
