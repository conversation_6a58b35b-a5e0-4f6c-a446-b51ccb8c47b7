import React from "react";
import { ResponsiveRenderer } from "@workspace/ui/components/responsive-renderer";
import MobileLoginByOtp, { LoginByOtpHandleSubmitFunction } from "@/app/login/components/login-by-otp/mobile/mobile-login-by-otp";

interface ILoginByOtpProps {
  handleSubmitOtp: LoginByOtpHandleSubmitFunction;
  secretKey?: string;
  mobile?: string;
}

function LoginByOtp({ handleSubmitOtp, mobile, secretKey }: ILoginByOtpProps) {
  return (
    <ResponsiveRenderer
      desktop={
        <MobileLoginByOtp
          secretKey={secretKey}
          mobile={mobile}
          handleSubmit={handleSubmitOtp}
        />
      }
      mobile={
        <MobileLoginByOtp
          secretKey={secretKey}
          mobile={mobile}
          handleSubmit={handleSubmitOtp}
        />
      }
    />
  );
}

export default LoginByOtp;
