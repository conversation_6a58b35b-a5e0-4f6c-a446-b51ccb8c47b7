import OtpTimer from "@/app/login/components/login-by-otp/otp-timer";
import { useOtpHook } from "@/app/login/hooks/useOtpHook";
import BackIcon from "@/assets/icons/arrow-right.svg";
import { ROUTE_LOGIN } from "@/constants/routes";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@workspace/ui/components/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
} from "@workspace/ui/components/form";
import { InputNumber } from "@workspace/ui/components/input-number";
import { Loading } from "@workspace/ui/components/loading";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useForm, UseFormSetError } from "react-hook-form";
import { z } from "zod";
import { WRONG_CODE_MESSAGE } from "@/app/login/utils";

const formSchema = z.object({
  otp: z.string().nonempty(" کد را وارد کنید."),
});

export type LoginByOtpFormValues = z.infer<typeof formSchema> & {
  secretKey: string;
};

export type SetErrorLoginByOtp = UseFormSetError<z.infer<typeof formSchema>>;

export type LoginByOtpHandleSubmitFunction = (
  values: LoginByOtpFormValues,
  { setError }: { setError: SetErrorLoginByOtp },
) => Promise<unknown>;

interface LoginByOtpProps {
  handleSubmit: LoginByOtpHandleSubmitFunction;
  secretKey?: string;
  mobile?: string;
}

const MAX_OTP_NUMBER = 6;

function MobileLoginByOtp({
  secretKey,
  handleSubmit,
  mobile,
}: LoginByOtpProps) {
  const router = useRouter();

  const { isLoadingSendOtp, onSendOtp, otpExpirationDate, otpSendDate } =
    useOtpHook({ secretKey });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      otp: "",
    },
  });

  const otp = form.watch("otp");

  function onSubmit(values: z.infer<typeof formSchema>) {
    return handleSubmit(
      { ...values, secretKey: secretKey || "" },
      { setError: form.setError },
    );
  }

  useEffect(() => {
    if (secretKey) {
      onSendOtp();
    }
  }, []);

  useEffect(() => {
    if (!mobile) {
      router.replace(ROUTE_LOGIN);
    }
  }, [mobile]);

  useEffect(() => {
    if (otp.length === MAX_OTP_NUMBER) {
      onSubmit({ otp });
    }
  }, [otp]);

  return (
    <div className="mt-12 flex flex-1 flex-col justify-between">
      <div className="flex flex-col justify-between gap-7">
        <div className="mb-2">
          <Link
            className="flex items-center gap-1"
            href={ROUTE_LOGIN}
            data-test="294b2833-5772-4393-bea9-6eb46fd50e8a"
          >
            <BackIcon className="text-text-nautral-default size-4" />
            <span className="text-text-nautral-default text-xs">
              ویرایش شماره
            </span>
          </Link>

          <p className="text-text-nautral-default mt-12 text-base font-bold">
            ورود{" "}
          </p>
          <p className="text-text-nautral-secondary mt-5 flex items-center text-xs">
            کد پیامک شده به شماره
            <span className="text-text-nautral-default mx-0.5">{mobile}</span>
            را وارد کنید.
          </p>
        </div>

        <Form {...form}>
          <form
            id="login-form"
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col justify-between"
          >
            <div className="flex flex-col justify-between">
              <FormField
                control={form.control}
                name="otp"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormControl>
                      <InputNumber
                        size="lg"
                        commaSeparated={false}
                        {...field}
                        placeholder="------"
                        autoFocus
                        maxLength={MAX_OTP_NUMBER}
                        onChange={(e) => {
                          field.onChange(e);
                          if (
                            fieldState?.error?.message === WRONG_CODE_MESSAGE
                          ) {
                            form.clearErrors("otp");
                          }
                        }}
                        rootClassName="!ps-2 !pe-5"
                        className="w-full text-center tracking-[14px] placeholder:text-center placeholder:tracking-[14px]"
                        dir="ltr"
                        helperText={
                          fieldState?.error?.message !== WRONG_CODE_MESSAGE
                            ? fieldState?.error?.message
                            : ""
                        }
                        aria-invalid={!!fieldState?.error?.message}
                        data-test="271a1882-cb0b-4d9b-98d5-172f69cf86d3"
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>

        <OtpTimer
          expirationDate={otpExpirationDate}
          otpSendDate={otpSendDate}
          onResend={onSendOtp}
          isLoading={isLoadingSendOtp}
        />
      </div>

      <Button
        variant="fill"
        size="md"
        type="submit"
        form="login-form"
        disabled={form.formState.isSubmitting}
        endAdornment={form.formState.isSubmitting && <Loading size="sm" />}
        data-test="602c0d8e-ff49-486c-9b5b-834e5280bac9"
      >
        ورود
      </Button>
    </div>
  );
}
export default MobileLoginByOtp;
