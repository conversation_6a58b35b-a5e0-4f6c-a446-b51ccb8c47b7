import { useAuthLogout } from "@workspace/investment-api/auto-generated/apis/auth/auth";
import { useRouter } from "next/navigation";
import { ROUTE_LOGIN } from "@/constants/routes";
import { removeAllCookies } from "@/hooks/useCookie";

export const useLogout = () => {
  const router = useRouter();
  const { data, isLoading, isError, refetch } = useAuthLogout({
    query: {
      enabled: false,
      retry: 0,
    },
  });

  const handleLogout = () => {
    refetch().then(() => {
      removeAllCookies();
      router.replace(ROUTE_LOGIN);
    });
  };

  return {
    logout: handleLogout,
    isLoading,
    isError,
    data,
  };
};
