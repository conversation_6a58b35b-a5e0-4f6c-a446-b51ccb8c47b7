import Cookies from "js-cookie";

export const setCookie = (
  name: string,
  value: string,
  options?: Cookies.CookieAttributes,
): string | undefined => Cookies.set(name, value, options);

export const getCookie = (name: string): string | undefined =>
  Cookies.get(name);

export const removeCookie = (
  name: string,
  options?: Cookies.CookieAttributes,
): void => Cookies.remove(name, options);

export const removeAllCookies = (): void => {
  // Get all cookie names
  const allCookies = Cookies.get();
  Object.keys(allCookies).forEach((cookieName) => {
    Cookies.remove(cookieName);
  });
};

const useCookie = () => ({
  setCookie,
  getCookie,
  removeCookie,
  removeAllCookies,
});

export default useCookie;
