import { apiService } from "@workspace/investment-api/apiService";
import { AxiosError, AxiosResponse } from "axios";

import { removeAllCookies } from "@/hooks/useCookie";
import { ROUTE_LOGIN } from "@/constants/routes";

const notAllowedStatusCodes = [401];

export const initializeApiInterceptors = () => {
  apiService.interceptors.response.use(
    (response: AxiosResponse) => {
      if (!response?.data?.isSuccess)
        throw new Error(response?.data?.errorMessage || "server has error");

      return response;
    },
    async (error: AxiosError) => {
      try {
        const { config, response } = error;

        // i.e. if the error is a network error, then we will show a notification
        // if (message === "Network Error" && typeof window !== "undefined") {
        //   console.error("Network error");
        // }

        if (
          config &&
          response &&
          notAllowedStatusCodes.includes(response.status) &&
          typeof window !== "undefined" &&
          ![ROUTE_LOGIN].includes(window.location.pathname)
        ) {
          removeAllCookies();
        }

        return Promise.reject(error);
      } catch (err) {
        // logout();
        return Promise.reject(err);
      }
    },
  );
};
