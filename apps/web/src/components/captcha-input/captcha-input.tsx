import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
} from "react";
import {
  GoogleCaptcha,
  GoogleCaptchaRef,
} from "@/components/captcha-input/components/google-captcha";
import {
  InputCaptcha,
  InputCaptchaRef,
} from "@/components/captcha-input/components/input-captcha";
import { CaptchaInputProps } from "@/components/captcha-input/types";
import { CaptchaTypeEnum } from "@workspace/investment-api/auto-generated/models";
import { GoogleReCaptchaProvider } from "react-google-recaptcha-v3";

const TWO_MINS = 120000;

export interface CaptchaInputRef {
  refreshCaptcha: () => Promise<void>;
  setInputValue?: (value: string) => void;
}

const CaptchaInput = forwardRef<CaptchaInputRef, CaptchaInputProps>(
  (
    {
      useCaptchaWhichCaptchaIsActive,
      useCaptchaRequestTextCaptcha,
      setErrorMessage,
      onCaptchaChange,
      googleRecaptchaSiteKey,
      className = "",
      error,
      inputProps,
      setIsLoading,
      setCaptchaType,
    },
    ref,
  ) => {
    const googleCaptchaRef = useRef<GoogleCaptchaRef>(null);
    const inputCaptchaRef = useRef<InputCaptchaRef>(null);

    const {
      data: captchaTypeData,
      isLoading: isLoadingType,
      error: captchaWhichCaptchaIsActiveError,
    } = useCaptchaWhichCaptchaIsActive();

    const captchaType = captchaTypeData?.data;

    const {
      data: textCaptchaData,
      isLoading: isLoadingTextCaptcha,
      refetch: refetchTextCaptcha,
      error: captchaRequestTextCaptchaError,
    } = useCaptchaRequestTextCaptcha({
      query: {
        enabled: !!captchaType && captchaType === CaptchaTypeEnum.Text,
        refetchInterval: TWO_MINS,
      },
    });

    const handleGoogleCaptchaChange = (secretKey: string | null) => {
      if (!secretKey) return;
      onCaptchaChange({ secretKey });
    };

    const handleImageCaptchaChange = (userInput: string) => {
      onCaptchaChange({
        captchaCode: userInput,
        secretKey: textCaptchaData?.data?.captchaSecretKey,
      });
    };

    const handleRefresh = async () => {
      if (captchaType === CaptchaTypeEnum.Text && refetchTextCaptcha) {
        refetchTextCaptcha();
      } else if (
        captchaType === CaptchaTypeEnum.GoogleRecaptchaV3 &&
        googleCaptchaRef.current
      ) {
        await googleCaptchaRef.current.refreshToken();
      }
    };

    useImperativeHandle(
      ref,
      () => ({
        refreshCaptcha: handleRefresh,
        setInputValue: (val: string) => inputCaptchaRef.current?.setValue(val),
      }),
      [captchaType, refetchTextCaptcha],
    );

    useEffect(() => {
      setErrorMessage?.(
        captchaWhichCaptchaIsActiveError?.response?.data?.errorMessage || "",
      );
    }, [captchaWhichCaptchaIsActiveError]);

    useEffect(() => {
      setErrorMessage?.(
        captchaRequestTextCaptchaError?.response?.data?.errorMessage || "",
      );
    }, [captchaRequestTextCaptchaError]);

    useEffect(() => {
      setIsLoading(!!isLoadingType || !!isLoadingTextCaptcha);
    }, [isLoadingType, isLoadingTextCaptcha]);

    useEffect(() => {
      if (captchaType) setCaptchaType?.(captchaType);
    }, [captchaType]);

    if (isLoadingType || captchaType === CaptchaTypeEnum.None) {
      return null;
    }

    if (captchaType === CaptchaTypeEnum.GoogleRecaptchaV3) {
      return (
        <div className={className}>
          <GoogleReCaptchaProvider
            reCaptchaKey={googleRecaptchaSiteKey}
            container={{ parameters: { badge: "bottomleft" } }}
          >
            <GoogleCaptcha
              ref={googleCaptchaRef}
              onCaptchaChange={handleGoogleCaptchaChange}
            />
          </GoogleReCaptchaProvider>
        </div>
      );
    }

    if (captchaType === CaptchaTypeEnum.Text) {
      return (
        <div className={className}>
          <InputCaptcha
            ref={inputCaptchaRef}
            captchaData={textCaptchaData}
            onRefresh={handleRefresh}
            onInputChange={handleImageCaptchaChange}
            isLoading={isLoadingTextCaptcha}
            error={error}
            {...inputProps}
          />
        </div>
      );
    }

    return null;
  },
);

CaptchaInput.displayName = "CaptchaInput";

export default CaptchaInput;
