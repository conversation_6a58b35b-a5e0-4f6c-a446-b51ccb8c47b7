import Graph from "@/assets/icons/graph.svg";
import Growt from "@/assets/icons/Growt.svg";
import Home from "@/components/navigation-bar/menu-items/home/<USER>";

function NavigationBar() {
  return (
    <div className="fixed right-0 bottom-0 left-0 z-50 mx-auto max-w-2xl px-2 pb-2">
      <div className="bg-surface-nautral-default-4 shadow-navigation flex w-full items-center justify-around rounded-2xl px-2 py-3">
        <div className="flex flex-col items-center justify-center gap-1">
          <Growt className="text-text-nautral-secondary size-6" />
          <div className="text-text-nautral-secondary text-[10px] font-normal">
            صندوق‌ها
          </div>
        </div>

        <div
          // className="p-1.5"
          style={
            {
              // borderRadius: "15.33px",
              // boxShadow: "0px 1.1px 1.1px 0px #DAB06640 inset",
            }
          }
        >
          <Home isSelected />
        </div>

        <div className="flex flex-col items-center justify-center gap-1">
          <Graph className="text-text-nautral-secondary size-6" />
          <div className="text-text-nautral-secondary text-[10px] font-normal">
            دارایی من
          </div>
        </div>
      </div>
    </div>
  );
}

export default NavigationBar;
