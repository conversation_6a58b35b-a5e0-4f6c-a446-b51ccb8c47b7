"use client";

import <PERSON><PERSON><PERSON><PERSON> from "@/assets/icons/Saba-logo.svg";
import { cn } from "@workspace/ui/lib/utils";
import { useTheme } from "next-themes";
import styles from "./home.module.css";

function Home({ isSelected = false }) {
  const { theme = "system" } = useTheme();

  return (
    <div
      className={cn(
        "overflow-hidden rounded-2xl p-2",
        styles.root,
        theme === "dark" ? styles.dark : styles.light,
      )}
      aria-selected={isSelected}
    >
      <SabaLogo className="size-8" />
    </div>
  );
}

export default Home;
