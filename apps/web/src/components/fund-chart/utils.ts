import { Options } from "highcharts";

const chartOption: Options = {
  xAxis: { visible: false },
  yAxis: {
    title: {
      text: "",
    },
    gridLineColor: "var(--border-nautral-disable)",
    gridLineWidth: 1,
    labels: {
      enabled: false,
    },
  },
  chart: { backgroundColor: "transparent" },
  plotOptions: {
    areaspline: {
      marker: {
        enabled: false,
        states: {
          hover: {
            enabled: true,
          },
        },
      },
    },
    spline: {
      marker: {
        enabled: false,
        states: {
          hover: {
            enabled: true,
          },
        },
      },
    },
  },
  title: { text: "" },
  subtitle: { text: "" },
  credits: { enabled: false },
  legend: {
    align: "right",
    verticalAlign: "top",
    x: 6,
    floating: false,
    borderWidth: 0,
    backgroundColor: "transparent",
    symbolHeight: 0,
    symbolWidth: 0,
    useHTML: true,
    // @ts-ignore
    labelFormatter: function () {
      // @ts-ignore
      return `<div style="color: var(--text-nautral-default)" class="flex"><span class="text-[10px] relative" style="bottom: 2px">${this.name}</span><div style="background-color: ${this?.visible ? this.color : "var(--border-nautral-disable)"}; border-radius: 4px; margin-left: 5px" class="w-3 h-3 inline-block"></div></div>`;
    },
  },
};

export default chartOption;
