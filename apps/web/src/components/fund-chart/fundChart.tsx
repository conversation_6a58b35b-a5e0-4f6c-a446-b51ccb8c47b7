"use client";

import Highcharts, { SeriesOptionsType } from "highcharts";
import HighchartsReact from "highcharts-react-official";
import { useMemo } from "react";
import chartOption from "./utils";

interface IFundChart {
  series: Array<SeriesOptionsType>;
}

function FundChart({ series }: IFundChart) {
  const options = useMemo(() => ({ ...chartOption, series }), series);

  return (
    <div className="chart-wrapper bg-surface-nautral-transparent h-full">
      <style>
        {`.highcharts-legend-item:not(:last-child)  > span {
        margin-left: 7px !important;}
        .highcharts-legend-item-hidden span {text-decoration: none !important;}`}
      </style>
      <HighchartsReact
        immutable
        containerProps={{
          className: "flex items-center justify-center h-full w-full",
        }}
        highcharts={Highcharts}
        options={options}
      />
    </div>
  );
}

export default FundChart;
