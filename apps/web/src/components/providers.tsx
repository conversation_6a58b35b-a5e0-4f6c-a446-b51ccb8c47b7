"use client";

import * as React from "react";
import { NuqsAdapter } from "nuqs/adapters/next/app";
import { Toaster } from "@workspace/ui/components/toast";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import ReactQueryProvider from "@workspace/investment-api/reactQueryProvider";
import { initializeApiInterceptors } from "@/utils/initializeApiInterceptors";

export function Providers({ children }: { children: React.ReactNode }) {
  React.useEffect(() => {
    initializeApiInterceptors();
  }, []);

  return (
    <NuqsAdapter>
      <ReactQueryProvider>
        <NextThemesProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
          enableColorScheme
        >
          {children}
          <Toaster />
        </NextThemesProvider>
      </ReactQueryProvider>
    </NuqsAdapter>
  );
}
