// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const addTypeObjectToMultipart = (openApiSpec: any) => {
  const newPaths = Object.fromEntries(
    Object.entries(openApiSpec.paths || {}).map(([path, methods]) => [
      path,
      Object.fromEntries(
        Object.entries(methods!).map(([method, operation]) => {
          if (
            operation.requestBody &&
            operation.requestBody.content &&
            operation.requestBody.content["multipart/form-data"]
          ) {
            const schema =
              operation.requestBody.content["multipart/form-data"].schema;
            if (schema && !schema.type) {
              schema.type = "object";
            }
          }
          return [method, operation];
        })
      ),
    ])
  );

  return {
    ...openApiSpec,
    paths: newPaths,
  };
};
