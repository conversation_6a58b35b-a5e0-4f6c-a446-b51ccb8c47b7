import { defineConfig } from "orval";
import { addTypeObjectToMultipart } from "./addTypeObjectToMultipart";

export default defineConfig({
  backend: {
    output: {
      mode: "tags-split",
      target: "./apis/backend.ts",
      schemas: "./models",
      client: "react-query",
      headers: true,
      clean: true,
      override: {
        mutator: {
          path: "./orval-custom-instance.ts",
          name: "api",
        },
      },
    },
    input: {
      target: "./swagger.json",
      override: {
        transformer: addTypeObjectToMultipart,
      },
    },
    hooks: {
      afterAllFilesWrite: ["prettier --write"],
    },
  },
});
