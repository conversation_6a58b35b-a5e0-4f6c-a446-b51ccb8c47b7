/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import { useMutation, useQuery } from "@tanstack/react-query";
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from "@tanstack/react-query";

import type {
  ApiResultOfBannerResponse,
  ApiResultOfBannersCountResponse,
  ApiResultOfBoolean,
  ApiResultOfGuid,
  ApiResultOfListOfBannerResponse,
  ApiResultOfPagingOutputOfBannerResponse,
  BannersGetBannersPagingParams,
  CreateBannerCommand,
  DeleteBannerCommand,
  UpdateBannerCommand,
} from "../../models";

import { api } from "../../orval-custom-instance";
import type { ErrorType } from "../../orval-custom-instance";

type SecondParameter<T extends (...args: never) => unknown> = Parameters<T>[1];

/**
 * @summary دریافت لیست بنرهای فعال
 */
export const bannersGetAllActiveBanners = (
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfListOfBannerResponse>(
    {
      url: `/api/general/v1/Banners/GetAllActiveBanners`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getBannersGetAllActiveBannersQueryKey = () => {
  return [`/api/general/v1/Banners/GetAllActiveBanners`] as const;
};

export const getBannersGetAllActiveBannersQueryOptions = <
  TData = Awaited<ReturnType<typeof bannersGetAllActiveBanners>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<
    UseQueryOptions<
      Awaited<ReturnType<typeof bannersGetAllActiveBanners>>,
      TError,
      TData
    >
  >;
  request?: SecondParameter<typeof api>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getBannersGetAllActiveBannersQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof bannersGetAllActiveBanners>>
  > = ({ signal }) => bannersGetAllActiveBanners(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof bannersGetAllActiveBanners>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type BannersGetAllActiveBannersQueryResult = NonNullable<
  Awaited<ReturnType<typeof bannersGetAllActiveBanners>>
>;
export type BannersGetAllActiveBannersQueryError = ErrorType<unknown>;

export function useBannersGetAllActiveBanners<
  TData = Awaited<ReturnType<typeof bannersGetAllActiveBanners>>,
  TError = ErrorType<unknown>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof bannersGetAllActiveBanners>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof bannersGetAllActiveBanners>>,
          TError,
          Awaited<ReturnType<typeof bannersGetAllActiveBanners>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useBannersGetAllActiveBanners<
  TData = Awaited<ReturnType<typeof bannersGetAllActiveBanners>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof bannersGetAllActiveBanners>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof bannersGetAllActiveBanners>>,
          TError,
          Awaited<ReturnType<typeof bannersGetAllActiveBanners>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useBannersGetAllActiveBanners<
  TData = Awaited<ReturnType<typeof bannersGetAllActiveBanners>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof bannersGetAllActiveBanners>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary دریافت لیست بنرهای فعال
 */

export function useBannersGetAllActiveBanners<
  TData = Awaited<ReturnType<typeof bannersGetAllActiveBanners>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof bannersGetAllActiveBanners>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getBannersGetAllActiveBannersQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary اضافه کردن بنر
 */
export const bannersCreateBanner = (
  createBannerCommand: CreateBannerCommand,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfGuid>(
    {
      url: `/api/backoffice/v1/Banners`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: createBannerCommand,
      signal,
    },
    options,
  );
};

export const getBannersCreateBannerMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof bannersCreateBanner>>,
    TError,
    { data: CreateBannerCommand },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof bannersCreateBanner>>,
  TError,
  { data: CreateBannerCommand },
  TContext
> => {
  const mutationKey = ["bannersCreateBanner"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof bannersCreateBanner>>,
    { data: CreateBannerCommand }
  > = (props) => {
    const { data } = props ?? {};

    return bannersCreateBanner(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type BannersCreateBannerMutationResult = NonNullable<
  Awaited<ReturnType<typeof bannersCreateBanner>>
>;
export type BannersCreateBannerMutationBody = CreateBannerCommand;
export type BannersCreateBannerMutationError = ErrorType<unknown>;

/**
 * @summary اضافه کردن بنر
 */
export const useBannersCreateBanner = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof bannersCreateBanner>>,
      TError,
      { data: CreateBannerCommand },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof bannersCreateBanner>>,
  TError,
  { data: CreateBannerCommand },
  TContext
> => {
  const mutationOptions = getBannersCreateBannerMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary ویرایش بنر
 */
export const bannersUpdateBanner = (
  updateBannerCommand: UpdateBannerCommand,
  options?: SecondParameter<typeof api>,
) => {
  return api<ApiResultOfBoolean>(
    {
      url: `/api/backoffice/v1/Banners/UpdateBanner`,
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      data: updateBannerCommand,
    },
    options,
  );
};

export const getBannersUpdateBannerMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof bannersUpdateBanner>>,
    TError,
    { data: UpdateBannerCommand },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof bannersUpdateBanner>>,
  TError,
  { data: UpdateBannerCommand },
  TContext
> => {
  const mutationKey = ["bannersUpdateBanner"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof bannersUpdateBanner>>,
    { data: UpdateBannerCommand }
  > = (props) => {
    const { data } = props ?? {};

    return bannersUpdateBanner(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type BannersUpdateBannerMutationResult = NonNullable<
  Awaited<ReturnType<typeof bannersUpdateBanner>>
>;
export type BannersUpdateBannerMutationBody = UpdateBannerCommand;
export type BannersUpdateBannerMutationError = ErrorType<unknown>;

/**
 * @summary ویرایش بنر
 */
export const useBannersUpdateBanner = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof bannersUpdateBanner>>,
      TError,
      { data: UpdateBannerCommand },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof bannersUpdateBanner>>,
  TError,
  { data: UpdateBannerCommand },
  TContext
> => {
  const mutationOptions = getBannersUpdateBannerMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary حذف بنر
 */
export const bannersDeleteBanner = (
  deleteBannerCommand: DeleteBannerCommand,
  options?: SecondParameter<typeof api>,
) => {
  return api<ApiResultOfBoolean>(
    {
      url: `/api/backoffice/v1/Banners/DeleteBanner`,
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      data: deleteBannerCommand,
    },
    options,
  );
};

export const getBannersDeleteBannerMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof bannersDeleteBanner>>,
    TError,
    { data: DeleteBannerCommand },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof bannersDeleteBanner>>,
  TError,
  { data: DeleteBannerCommand },
  TContext
> => {
  const mutationKey = ["bannersDeleteBanner"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof bannersDeleteBanner>>,
    { data: DeleteBannerCommand }
  > = (props) => {
    const { data } = props ?? {};

    return bannersDeleteBanner(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type BannersDeleteBannerMutationResult = NonNullable<
  Awaited<ReturnType<typeof bannersDeleteBanner>>
>;
export type BannersDeleteBannerMutationBody = DeleteBannerCommand;
export type BannersDeleteBannerMutationError = ErrorType<unknown>;

/**
 * @summary حذف بنر
 */
export const useBannersDeleteBanner = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof bannersDeleteBanner>>,
      TError,
      { data: DeleteBannerCommand },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof bannersDeleteBanner>>,
  TError,
  { data: DeleteBannerCommand },
  TContext
> => {
  const mutationOptions = getBannersDeleteBannerMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary دریافت بنر با آیدی
 */
export const bannersGetBannerById = (
  id: string,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfBannerResponse>(
    {
      url: `/api/backoffice/v1/Banners/GetBannerById/${id}`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getBannersGetBannerByIdQueryKey = (id: string) => {
  return [`/api/backoffice/v1/Banners/GetBannerById/${id}`] as const;
};

export const getBannersGetBannerByIdQueryOptions = <
  TData = Awaited<ReturnType<typeof bannersGetBannerById>>,
  TError = ErrorType<unknown>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof bannersGetBannerById>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getBannersGetBannerByIdQueryKey(id);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof bannersGetBannerById>>
  > = ({ signal }) => bannersGetBannerById(id, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!id,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof bannersGetBannerById>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type BannersGetBannerByIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof bannersGetBannerById>>
>;
export type BannersGetBannerByIdQueryError = ErrorType<unknown>;

export function useBannersGetBannerById<
  TData = Awaited<ReturnType<typeof bannersGetBannerById>>,
  TError = ErrorType<unknown>,
>(
  id: string,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof bannersGetBannerById>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof bannersGetBannerById>>,
          TError,
          Awaited<ReturnType<typeof bannersGetBannerById>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useBannersGetBannerById<
  TData = Awaited<ReturnType<typeof bannersGetBannerById>>,
  TError = ErrorType<unknown>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof bannersGetBannerById>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof bannersGetBannerById>>,
          TError,
          Awaited<ReturnType<typeof bannersGetBannerById>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useBannersGetBannerById<
  TData = Awaited<ReturnType<typeof bannersGetBannerById>>,
  TError = ErrorType<unknown>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof bannersGetBannerById>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary دریافت بنر با آیدی
 */

export function useBannersGetBannerById<
  TData = Awaited<ReturnType<typeof bannersGetBannerById>>,
  TError = ErrorType<unknown>,
>(
  id: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof bannersGetBannerById>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getBannersGetBannerByIdQueryOptions(id, options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary پیجینگ بنر
 */
export const bannersGetBannersPaging = (
  params?: BannersGetBannersPagingParams,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfPagingOutputOfBannerResponse>(
    {
      url: `/api/backoffice/v1/Banners/GetBannersPaging`,
      method: "GET",
      params,
      signal,
    },
    options,
  );
};

export const getBannersGetBannersPagingQueryKey = (
  params?: BannersGetBannersPagingParams,
) => {
  return [
    `/api/backoffice/v1/Banners/GetBannersPaging`,
    ...(params ? [params] : []),
  ] as const;
};

export const getBannersGetBannersPagingQueryOptions = <
  TData = Awaited<ReturnType<typeof bannersGetBannersPaging>>,
  TError = ErrorType<unknown>,
>(
  params?: BannersGetBannersPagingParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof bannersGetBannersPaging>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getBannersGetBannersPagingQueryKey(params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof bannersGetBannersPaging>>
  > = ({ signal }) => bannersGetBannersPaging(params, requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof bannersGetBannersPaging>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type BannersGetBannersPagingQueryResult = NonNullable<
  Awaited<ReturnType<typeof bannersGetBannersPaging>>
>;
export type BannersGetBannersPagingQueryError = ErrorType<unknown>;

export function useBannersGetBannersPaging<
  TData = Awaited<ReturnType<typeof bannersGetBannersPaging>>,
  TError = ErrorType<unknown>,
>(
  params: undefined | BannersGetBannersPagingParams,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof bannersGetBannersPaging>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof bannersGetBannersPaging>>,
          TError,
          Awaited<ReturnType<typeof bannersGetBannersPaging>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useBannersGetBannersPaging<
  TData = Awaited<ReturnType<typeof bannersGetBannersPaging>>,
  TError = ErrorType<unknown>,
>(
  params?: BannersGetBannersPagingParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof bannersGetBannersPaging>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof bannersGetBannersPaging>>,
          TError,
          Awaited<ReturnType<typeof bannersGetBannersPaging>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useBannersGetBannersPaging<
  TData = Awaited<ReturnType<typeof bannersGetBannersPaging>>,
  TError = ErrorType<unknown>,
>(
  params?: BannersGetBannersPagingParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof bannersGetBannersPaging>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary پیجینگ بنر
 */

export function useBannersGetBannersPaging<
  TData = Awaited<ReturnType<typeof bannersGetBannersPaging>>,
  TError = ErrorType<unknown>,
>(
  params?: BannersGetBannersPagingParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof bannersGetBannersPaging>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getBannersGetBannersPagingQueryOptions(params, options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary تعداد بنرها
 */
export const bannersGetBannersCount = (
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfBannersCountResponse>(
    {
      url: `/api/backoffice/v1/Banners/GetBannersCount`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getBannersGetBannersCountQueryKey = () => {
  return [`/api/backoffice/v1/Banners/GetBannersCount`] as const;
};

export const getBannersGetBannersCountQueryOptions = <
  TData = Awaited<ReturnType<typeof bannersGetBannersCount>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<
    UseQueryOptions<
      Awaited<ReturnType<typeof bannersGetBannersCount>>,
      TError,
      TData
    >
  >;
  request?: SecondParameter<typeof api>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getBannersGetBannersCountQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof bannersGetBannersCount>>
  > = ({ signal }) => bannersGetBannersCount(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof bannersGetBannersCount>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type BannersGetBannersCountQueryResult = NonNullable<
  Awaited<ReturnType<typeof bannersGetBannersCount>>
>;
export type BannersGetBannersCountQueryError = ErrorType<unknown>;

export function useBannersGetBannersCount<
  TData = Awaited<ReturnType<typeof bannersGetBannersCount>>,
  TError = ErrorType<unknown>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof bannersGetBannersCount>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof bannersGetBannersCount>>,
          TError,
          Awaited<ReturnType<typeof bannersGetBannersCount>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useBannersGetBannersCount<
  TData = Awaited<ReturnType<typeof bannersGetBannersCount>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof bannersGetBannersCount>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof bannersGetBannersCount>>,
          TError,
          Awaited<ReturnType<typeof bannersGetBannersCount>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useBannersGetBannersCount<
  TData = Awaited<ReturnType<typeof bannersGetBannersCount>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof bannersGetBannersCount>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary تعداد بنرها
 */

export function useBannersGetBannersCount<
  TData = Awaited<ReturnType<typeof bannersGetBannersCount>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof bannersGetBannersCount>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getBannersGetBannersCountQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}
