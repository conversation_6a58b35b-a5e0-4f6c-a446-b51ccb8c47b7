/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import { useMutation, useQuery } from "@tanstack/react-query";
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from "@tanstack/react-query";

import type {
  ApiResultOfBoolean,
  ApiResultOfString,
  EditCurrentUserFrontendConfigCommand,
} from "../../models";

import { api } from "../../orval-custom-instance";
import type { ErrorType } from "../../orval-custom-instance";

type SecondParameter<T extends (...args: never) => unknown> = Parameters<T>[1];

/**
 * @summary ویرایش تنظیمات کاربر
 */
export const userFrontendConfigEditUserConfig = (
  editCurrentUserFrontendConfigCommand: EditCurrentUserFrontendConfigCommand,
  options?: SecondParameter<typeof api>,
) => {
  return api<ApiResultOfBoolean>(
    {
      url: `/api/general/v1/UserFrontendConfig/EditUserConfig`,
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      data: editCurrentUserFrontendConfigCommand,
    },
    options,
  );
};

export const getUserFrontendConfigEditUserConfigMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof userFrontendConfigEditUserConfig>>,
    TError,
    { data: EditCurrentUserFrontendConfigCommand },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof userFrontendConfigEditUserConfig>>,
  TError,
  { data: EditCurrentUserFrontendConfigCommand },
  TContext
> => {
  const mutationKey = ["userFrontendConfigEditUserConfig"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof userFrontendConfigEditUserConfig>>,
    { data: EditCurrentUserFrontendConfigCommand }
  > = (props) => {
    const { data } = props ?? {};

    return userFrontendConfigEditUserConfig(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type UserFrontendConfigEditUserConfigMutationResult = NonNullable<
  Awaited<ReturnType<typeof userFrontendConfigEditUserConfig>>
>;
export type UserFrontendConfigEditUserConfigMutationBody =
  EditCurrentUserFrontendConfigCommand;
export type UserFrontendConfigEditUserConfigMutationError = ErrorType<unknown>;

/**
 * @summary ویرایش تنظیمات کاربر
 */
export const useUserFrontendConfigEditUserConfig = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof userFrontendConfigEditUserConfig>>,
      TError,
      { data: EditCurrentUserFrontendConfigCommand },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof userFrontendConfigEditUserConfig>>,
  TError,
  { data: EditCurrentUserFrontendConfigCommand },
  TContext
> => {
  const mutationOptions =
    getUserFrontendConfigEditUserConfigMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary دریافت تنظیمات کاربر
 */
export const userFrontendConfigGetUserConfig = (
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfString>(
    {
      url: `/api/general/v1/UserFrontendConfig/GetUserConfig`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getUserFrontendConfigGetUserConfigQueryKey = () => {
  return [`/api/general/v1/UserFrontendConfig/GetUserConfig`] as const;
};

export const getUserFrontendConfigGetUserConfigQueryOptions = <
  TData = Awaited<ReturnType<typeof userFrontendConfigGetUserConfig>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<
    UseQueryOptions<
      Awaited<ReturnType<typeof userFrontendConfigGetUserConfig>>,
      TError,
      TData
    >
  >;
  request?: SecondParameter<typeof api>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getUserFrontendConfigGetUserConfigQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof userFrontendConfigGetUserConfig>>
  > = ({ signal }) => userFrontendConfigGetUserConfig(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof userFrontendConfigGetUserConfig>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type UserFrontendConfigGetUserConfigQueryResult = NonNullable<
  Awaited<ReturnType<typeof userFrontendConfigGetUserConfig>>
>;
export type UserFrontendConfigGetUserConfigQueryError = ErrorType<unknown>;

export function useUserFrontendConfigGetUserConfig<
  TData = Awaited<ReturnType<typeof userFrontendConfigGetUserConfig>>,
  TError = ErrorType<unknown>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof userFrontendConfigGetUserConfig>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof userFrontendConfigGetUserConfig>>,
          TError,
          Awaited<ReturnType<typeof userFrontendConfigGetUserConfig>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useUserFrontendConfigGetUserConfig<
  TData = Awaited<ReturnType<typeof userFrontendConfigGetUserConfig>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof userFrontendConfigGetUserConfig>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof userFrontendConfigGetUserConfig>>,
          TError,
          Awaited<ReturnType<typeof userFrontendConfigGetUserConfig>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useUserFrontendConfigGetUserConfig<
  TData = Awaited<ReturnType<typeof userFrontendConfigGetUserConfig>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof userFrontendConfigGetUserConfig>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary دریافت تنظیمات کاربر
 */

export function useUserFrontendConfigGetUserConfig<
  TData = Awaited<ReturnType<typeof userFrontendConfigGetUserConfig>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof userFrontendConfigGetUserConfig>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getUserFrontendConfigGetUserConfigQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}
