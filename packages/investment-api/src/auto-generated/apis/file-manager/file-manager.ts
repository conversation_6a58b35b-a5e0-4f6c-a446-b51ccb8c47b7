/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import { useMutation, useQuery } from "@tanstack/react-query";
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from "@tanstack/react-query";

import type {
  ApiResultOfString,
  FileManagerSaveFileBody,
  FileManagerSaveImageBody,
} from "../../models";

import { api } from "../../orval-custom-instance";
import type { ErrorType } from "../../orval-custom-instance";

type SecondParameter<T extends (...args: never) => unknown> = Parameters<T>[1];

/**
 * @summary نمایش تصویر - سمت کاربران
 */
export const fileManagerShowImage = (
  fileName: string,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<Blob>(
    {
      url: `/api/general/v1/FileManager/ShowImage/${fileName}`,
      method: "GET",
      responseType: "blob",
      signal,
    },
    options,
  );
};

export const getFileManagerShowImageQueryKey = (fileName: string) => {
  return [`/api/general/v1/FileManager/ShowImage/${fileName}`] as const;
};

export const getFileManagerShowImageQueryOptions = <
  TData = Awaited<ReturnType<typeof fileManagerShowImage>>,
  TError = ErrorType<unknown>,
>(
  fileName: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fileManagerShowImage>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getFileManagerShowImageQueryKey(fileName);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof fileManagerShowImage>>
  > = ({ signal }) => fileManagerShowImage(fileName, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!fileName,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof fileManagerShowImage>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type FileManagerShowImageQueryResult = NonNullable<
  Awaited<ReturnType<typeof fileManagerShowImage>>
>;
export type FileManagerShowImageQueryError = ErrorType<unknown>;

export function useFileManagerShowImage<
  TData = Awaited<ReturnType<typeof fileManagerShowImage>>,
  TError = ErrorType<unknown>,
>(
  fileName: string,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fileManagerShowImage>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof fileManagerShowImage>>,
          TError,
          Awaited<ReturnType<typeof fileManagerShowImage>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useFileManagerShowImage<
  TData = Awaited<ReturnType<typeof fileManagerShowImage>>,
  TError = ErrorType<unknown>,
>(
  fileName: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fileManagerShowImage>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof fileManagerShowImage>>,
          TError,
          Awaited<ReturnType<typeof fileManagerShowImage>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useFileManagerShowImage<
  TData = Awaited<ReturnType<typeof fileManagerShowImage>>,
  TError = ErrorType<unknown>,
>(
  fileName: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fileManagerShowImage>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary نمایش تصویر - سمت کاربران
 */

export function useFileManagerShowImage<
  TData = Awaited<ReturnType<typeof fileManagerShowImage>>,
  TError = ErrorType<unknown>,
>(
  fileName: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fileManagerShowImage>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getFileManagerShowImageQueryOptions(fileName, options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary دانلود فایل - سمت کاربران
 */
export const fileManagerDownloadFile = (
  fileName: string,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<Blob>(
    {
      url: `/api/general/v1/FileManager/DownloadFile/${fileName}`,
      method: "GET",
      responseType: "blob",
      signal,
    },
    options,
  );
};

export const getFileManagerDownloadFileQueryKey = (fileName: string) => {
  return [`/api/general/v1/FileManager/DownloadFile/${fileName}`] as const;
};

export const getFileManagerDownloadFileQueryOptions = <
  TData = Awaited<ReturnType<typeof fileManagerDownloadFile>>,
  TError = ErrorType<unknown>,
>(
  fileName: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fileManagerDownloadFile>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getFileManagerDownloadFileQueryKey(fileName);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof fileManagerDownloadFile>>
  > = ({ signal }) => fileManagerDownloadFile(fileName, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!fileName,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof fileManagerDownloadFile>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type FileManagerDownloadFileQueryResult = NonNullable<
  Awaited<ReturnType<typeof fileManagerDownloadFile>>
>;
export type FileManagerDownloadFileQueryError = ErrorType<unknown>;

export function useFileManagerDownloadFile<
  TData = Awaited<ReturnType<typeof fileManagerDownloadFile>>,
  TError = ErrorType<unknown>,
>(
  fileName: string,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fileManagerDownloadFile>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof fileManagerDownloadFile>>,
          TError,
          Awaited<ReturnType<typeof fileManagerDownloadFile>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useFileManagerDownloadFile<
  TData = Awaited<ReturnType<typeof fileManagerDownloadFile>>,
  TError = ErrorType<unknown>,
>(
  fileName: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fileManagerDownloadFile>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof fileManagerDownloadFile>>,
          TError,
          Awaited<ReturnType<typeof fileManagerDownloadFile>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useFileManagerDownloadFile<
  TData = Awaited<ReturnType<typeof fileManagerDownloadFile>>,
  TError = ErrorType<unknown>,
>(
  fileName: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fileManagerDownloadFile>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary دانلود فایل - سمت کاربران
 */

export function useFileManagerDownloadFile<
  TData = Awaited<ReturnType<typeof fileManagerDownloadFile>>,
  TError = ErrorType<unknown>,
>(
  fileName: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fileManagerDownloadFile>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getFileManagerDownloadFileQueryOptions(
    fileName,
    options,
  );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary ذخیره تصویر
 */
export const fileManagerSaveImage = (
  fileManagerSaveImageBody: FileManagerSaveImageBody,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  const formData = new FormData();
  if (
    fileManagerSaveImageBody.ContentType !== undefined &&
    fileManagerSaveImageBody.ContentType !== null
  ) {
    formData.append(`ContentType`, fileManagerSaveImageBody.ContentType);
  }
  if (
    fileManagerSaveImageBody.ContentDisposition !== undefined &&
    fileManagerSaveImageBody.ContentDisposition !== null
  ) {
    formData.append(
      `ContentDisposition`,
      fileManagerSaveImageBody.ContentDisposition,
    );
  }
  if (
    fileManagerSaveImageBody.Headers !== undefined &&
    fileManagerSaveImageBody.Headers !== null
  ) {
    fileManagerSaveImageBody.Headers.forEach((value) =>
      formData.append(`Headers`, value),
    );
  }
  if (fileManagerSaveImageBody.Length !== undefined) {
    formData.append(`Length`, fileManagerSaveImageBody.Length.toString());
  }
  if (
    fileManagerSaveImageBody.Name !== undefined &&
    fileManagerSaveImageBody.Name !== null
  ) {
    formData.append(`Name`, fileManagerSaveImageBody.Name);
  }
  if (
    fileManagerSaveImageBody.FileName !== undefined &&
    fileManagerSaveImageBody.FileName !== null
  ) {
    formData.append(`FileName`, fileManagerSaveImageBody.FileName);
  }

  return api<ApiResultOfString>(
    {
      url: `/api/backoffice/v1/FileManager/SaveImage`,
      method: "POST",
      headers: { "Content-Type": "multipart/form-data" },
      data: formData,
      signal,
    },
    options,
  );
};

export const getFileManagerSaveImageMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof fileManagerSaveImage>>,
    TError,
    { data: FileManagerSaveImageBody },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof fileManagerSaveImage>>,
  TError,
  { data: FileManagerSaveImageBody },
  TContext
> => {
  const mutationKey = ["fileManagerSaveImage"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof fileManagerSaveImage>>,
    { data: FileManagerSaveImageBody }
  > = (props) => {
    const { data } = props ?? {};

    return fileManagerSaveImage(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type FileManagerSaveImageMutationResult = NonNullable<
  Awaited<ReturnType<typeof fileManagerSaveImage>>
>;
export type FileManagerSaveImageMutationBody = FileManagerSaveImageBody;
export type FileManagerSaveImageMutationError = ErrorType<unknown>;

/**
 * @summary ذخیره تصویر
 */
export const useFileManagerSaveImage = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof fileManagerSaveImage>>,
      TError,
      { data: FileManagerSaveImageBody },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof fileManagerSaveImage>>,
  TError,
  { data: FileManagerSaveImageBody },
  TContext
> => {
  const mutationOptions = getFileManagerSaveImageMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary نمایش تصویر - بک آفیس
 */
export const fileManagerShowImage2 = (
  fileName: string,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<Blob>(
    {
      url: `/api/backoffice/v1/FileManager/ShowImage/${fileName}`,
      method: "GET",
      responseType: "blob",
      signal,
    },
    options,
  );
};

export const getFileManagerShowImage2QueryKey = (fileName: string) => {
  return [`/api/backoffice/v1/FileManager/ShowImage/${fileName}`] as const;
};

export const getFileManagerShowImage2QueryOptions = <
  TData = Awaited<ReturnType<typeof fileManagerShowImage2>>,
  TError = ErrorType<unknown>,
>(
  fileName: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fileManagerShowImage2>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getFileManagerShowImage2QueryKey(fileName);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof fileManagerShowImage2>>
  > = ({ signal }) => fileManagerShowImage2(fileName, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!fileName,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof fileManagerShowImage2>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type FileManagerShowImage2QueryResult = NonNullable<
  Awaited<ReturnType<typeof fileManagerShowImage2>>
>;
export type FileManagerShowImage2QueryError = ErrorType<unknown>;

export function useFileManagerShowImage2<
  TData = Awaited<ReturnType<typeof fileManagerShowImage2>>,
  TError = ErrorType<unknown>,
>(
  fileName: string,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fileManagerShowImage2>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof fileManagerShowImage2>>,
          TError,
          Awaited<ReturnType<typeof fileManagerShowImage2>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useFileManagerShowImage2<
  TData = Awaited<ReturnType<typeof fileManagerShowImage2>>,
  TError = ErrorType<unknown>,
>(
  fileName: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fileManagerShowImage2>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof fileManagerShowImage2>>,
          TError,
          Awaited<ReturnType<typeof fileManagerShowImage2>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useFileManagerShowImage2<
  TData = Awaited<ReturnType<typeof fileManagerShowImage2>>,
  TError = ErrorType<unknown>,
>(
  fileName: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fileManagerShowImage2>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary نمایش تصویر - بک آفیس
 */

export function useFileManagerShowImage2<
  TData = Awaited<ReturnType<typeof fileManagerShowImage2>>,
  TError = ErrorType<unknown>,
>(
  fileName: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fileManagerShowImage2>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getFileManagerShowImage2QueryOptions(fileName, options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary ذخیره فایل
 */
export const fileManagerSaveFile = (
  fileManagerSaveFileBody: FileManagerSaveFileBody,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  const formData = new FormData();
  if (
    fileManagerSaveFileBody.ContentType !== undefined &&
    fileManagerSaveFileBody.ContentType !== null
  ) {
    formData.append(`ContentType`, fileManagerSaveFileBody.ContentType);
  }
  if (
    fileManagerSaveFileBody.ContentDisposition !== undefined &&
    fileManagerSaveFileBody.ContentDisposition !== null
  ) {
    formData.append(
      `ContentDisposition`,
      fileManagerSaveFileBody.ContentDisposition,
    );
  }
  if (
    fileManagerSaveFileBody.Headers !== undefined &&
    fileManagerSaveFileBody.Headers !== null
  ) {
    fileManagerSaveFileBody.Headers.forEach((value) =>
      formData.append(`Headers`, value),
    );
  }
  if (fileManagerSaveFileBody.Length !== undefined) {
    formData.append(`Length`, fileManagerSaveFileBody.Length.toString());
  }
  if (
    fileManagerSaveFileBody.Name !== undefined &&
    fileManagerSaveFileBody.Name !== null
  ) {
    formData.append(`Name`, fileManagerSaveFileBody.Name);
  }
  if (
    fileManagerSaveFileBody.FileName !== undefined &&
    fileManagerSaveFileBody.FileName !== null
  ) {
    formData.append(`FileName`, fileManagerSaveFileBody.FileName);
  }

  return api<ApiResultOfString>(
    {
      url: `/api/backoffice/v1/FileManager/SaveFile`,
      method: "POST",
      headers: { "Content-Type": "multipart/form-data" },
      data: formData,
      signal,
    },
    options,
  );
};

export const getFileManagerSaveFileMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof fileManagerSaveFile>>,
    TError,
    { data: FileManagerSaveFileBody },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof fileManagerSaveFile>>,
  TError,
  { data: FileManagerSaveFileBody },
  TContext
> => {
  const mutationKey = ["fileManagerSaveFile"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof fileManagerSaveFile>>,
    { data: FileManagerSaveFileBody }
  > = (props) => {
    const { data } = props ?? {};

    return fileManagerSaveFile(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type FileManagerSaveFileMutationResult = NonNullable<
  Awaited<ReturnType<typeof fileManagerSaveFile>>
>;
export type FileManagerSaveFileMutationBody = FileManagerSaveFileBody;
export type FileManagerSaveFileMutationError = ErrorType<unknown>;

/**
 * @summary ذخیره فایل
 */
export const useFileManagerSaveFile = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof fileManagerSaveFile>>,
      TError,
      { data: FileManagerSaveFileBody },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof fileManagerSaveFile>>,
  TError,
  { data: FileManagerSaveFileBody },
  TContext
> => {
  const mutationOptions = getFileManagerSaveFileMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary دانلود فایل - بک آفیس
 */
export const fileManagerDownloadFile2 = (
  fileName: string,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<Blob>(
    {
      url: `/api/backoffice/v1/FileManager/DownloadFile/${fileName}`,
      method: "GET",
      responseType: "blob",
      signal,
    },
    options,
  );
};

export const getFileManagerDownloadFile2QueryKey = (fileName: string) => {
  return [`/api/backoffice/v1/FileManager/DownloadFile/${fileName}`] as const;
};

export const getFileManagerDownloadFile2QueryOptions = <
  TData = Awaited<ReturnType<typeof fileManagerDownloadFile2>>,
  TError = ErrorType<unknown>,
>(
  fileName: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fileManagerDownloadFile2>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getFileManagerDownloadFile2QueryKey(fileName);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof fileManagerDownloadFile2>>
  > = ({ signal }) =>
    fileManagerDownloadFile2(fileName, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!fileName,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof fileManagerDownloadFile2>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type FileManagerDownloadFile2QueryResult = NonNullable<
  Awaited<ReturnType<typeof fileManagerDownloadFile2>>
>;
export type FileManagerDownloadFile2QueryError = ErrorType<unknown>;

export function useFileManagerDownloadFile2<
  TData = Awaited<ReturnType<typeof fileManagerDownloadFile2>>,
  TError = ErrorType<unknown>,
>(
  fileName: string,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fileManagerDownloadFile2>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof fileManagerDownloadFile2>>,
          TError,
          Awaited<ReturnType<typeof fileManagerDownloadFile2>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useFileManagerDownloadFile2<
  TData = Awaited<ReturnType<typeof fileManagerDownloadFile2>>,
  TError = ErrorType<unknown>,
>(
  fileName: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fileManagerDownloadFile2>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof fileManagerDownloadFile2>>,
          TError,
          Awaited<ReturnType<typeof fileManagerDownloadFile2>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useFileManagerDownloadFile2<
  TData = Awaited<ReturnType<typeof fileManagerDownloadFile2>>,
  TError = ErrorType<unknown>,
>(
  fileName: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fileManagerDownloadFile2>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary دانلود فایل - بک آفیس
 */

export function useFileManagerDownloadFile2<
  TData = Awaited<ReturnType<typeof fileManagerDownloadFile2>>,
  TError = ErrorType<unknown>,
>(
  fileName: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof fileManagerDownloadFile2>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getFileManagerDownloadFile2QueryOptions(
    fileName,
    options,
  );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}
