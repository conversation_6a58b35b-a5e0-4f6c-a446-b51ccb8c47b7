/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import { useQuery } from "@tanstack/react-query";
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseQueryOptions,
  UseQueryResult,
} from "@tanstack/react-query";

import type { ApiResultOfGetFundTimePeriodBasedSimpleReturnsResponse } from "../../models";

import { api } from "../../orval-custom-instance";
import type { ErrorType } from "../../orval-custom-instance";

type SecondParameter<T extends (...args: never) => unknown> = Parameters<T>[1];

/**
 * @summary دریافت بازدهی صندوق در دوره های زمانی 1،3،6 و 12 ماهه
 */
export const simpleReturnReportsGetFundTimePeriodSimpleReturns = (
  fundId: string,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfGetFundTimePeriodBasedSimpleReturnsResponse>(
    {
      url: `/api/general/v1/SimpleReturnReports/GetFundTimePeriodSimpleReturns/${fundId}`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getSimpleReturnReportsGetFundTimePeriodSimpleReturnsQueryKey = (
  fundId: string,
) => {
  return [
    `/api/general/v1/SimpleReturnReports/GetFundTimePeriodSimpleReturns/${fundId}`,
  ] as const;
};

export const getSimpleReturnReportsGetFundTimePeriodSimpleReturnsQueryOptions =
  <
    TData = Awaited<
      ReturnType<typeof simpleReturnReportsGetFundTimePeriodSimpleReturns>
    >,
    TError = ErrorType<unknown>,
  >(
    fundId: string,
    options?: {
      query?: Partial<
        UseQueryOptions<
          Awaited<
            ReturnType<typeof simpleReturnReportsGetFundTimePeriodSimpleReturns>
          >,
          TError,
          TData
        >
      >;
      request?: SecondParameter<typeof api>;
    },
  ) => {
    const { query: queryOptions, request: requestOptions } = options ?? {};

    const queryKey =
      queryOptions?.queryKey ??
      getSimpleReturnReportsGetFundTimePeriodSimpleReturnsQueryKey(fundId);

    const queryFn: QueryFunction<
      Awaited<
        ReturnType<typeof simpleReturnReportsGetFundTimePeriodSimpleReturns>
      >
    > = ({ signal }) =>
      simpleReturnReportsGetFundTimePeriodSimpleReturns(
        fundId,
        requestOptions,
        signal,
      );

    return {
      queryKey,
      queryFn,
      enabled: !!fundId,
      ...queryOptions,
    } as UseQueryOptions<
      Awaited<
        ReturnType<typeof simpleReturnReportsGetFundTimePeriodSimpleReturns>
      >,
      TError,
      TData
    > & { queryKey: DataTag<QueryKey, TData, TError> };
  };

export type SimpleReturnReportsGetFundTimePeriodSimpleReturnsQueryResult =
  NonNullable<
    Awaited<
      ReturnType<typeof simpleReturnReportsGetFundTimePeriodSimpleReturns>
    >
  >;
export type SimpleReturnReportsGetFundTimePeriodSimpleReturnsQueryError =
  ErrorType<unknown>;

export function useSimpleReturnReportsGetFundTimePeriodSimpleReturns<
  TData = Awaited<
    ReturnType<typeof simpleReturnReportsGetFundTimePeriodSimpleReturns>
  >,
  TError = ErrorType<unknown>,
>(
  fundId: string,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof simpleReturnReportsGetFundTimePeriodSimpleReturns>
        >,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<
            ReturnType<typeof simpleReturnReportsGetFundTimePeriodSimpleReturns>
          >,
          TError,
          Awaited<
            ReturnType<typeof simpleReturnReportsGetFundTimePeriodSimpleReturns>
          >
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useSimpleReturnReportsGetFundTimePeriodSimpleReturns<
  TData = Awaited<
    ReturnType<typeof simpleReturnReportsGetFundTimePeriodSimpleReturns>
  >,
  TError = ErrorType<unknown>,
>(
  fundId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof simpleReturnReportsGetFundTimePeriodSimpleReturns>
        >,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<
            ReturnType<typeof simpleReturnReportsGetFundTimePeriodSimpleReturns>
          >,
          TError,
          Awaited<
            ReturnType<typeof simpleReturnReportsGetFundTimePeriodSimpleReturns>
          >
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useSimpleReturnReportsGetFundTimePeriodSimpleReturns<
  TData = Awaited<
    ReturnType<typeof simpleReturnReportsGetFundTimePeriodSimpleReturns>
  >,
  TError = ErrorType<unknown>,
>(
  fundId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof simpleReturnReportsGetFundTimePeriodSimpleReturns>
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary دریافت بازدهی صندوق در دوره های زمانی 1،3،6 و 12 ماهه
 */

export function useSimpleReturnReportsGetFundTimePeriodSimpleReturns<
  TData = Awaited<
    ReturnType<typeof simpleReturnReportsGetFundTimePeriodSimpleReturns>
  >,
  TError = ErrorType<unknown>,
>(
  fundId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<
          ReturnType<typeof simpleReturnReportsGetFundTimePeriodSimpleReturns>
        >,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions =
    getSimpleReturnReportsGetFundTimePeriodSimpleReturnsQueryOptions(
      fundId,
      options,
    );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}
