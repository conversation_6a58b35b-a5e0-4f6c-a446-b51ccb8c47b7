/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Investment API
 * Saba Tamin Investment Project
 * OpenAPI spec version: v1
 */
import { useMutation, useQuery } from "@tanstack/react-query";
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from "@tanstack/react-query";

import type {
  ApiResultOfBoolean,
  ApiResultOfGetOtpResponse,
  ApiResultOfLoginMethodResponse,
  AuthLoginBody,
  AuthLoginByOTPBody,
  AuthLoginByPasswordBody,
  AuthSendLoginOTPBody,
} from "../../models";

import { api } from "../../orval-custom-instance";
import type { ErrorType } from "../../orval-custom-instance";

type SecondParameter<T extends (...args: never) => unknown> = Parameters<T>[1];

/**
 * @summary دریافت روش لاگین کاربر
 */
export const authGetLoginMethod = (
  mobile: string,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfLoginMethodResponse>(
    {
      url: `/api/general/v1/Auth/GetLoginMethod/${mobile}`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getAuthGetLoginMethodQueryKey = (mobile: string) => {
  return [`/api/general/v1/Auth/GetLoginMethod/${mobile}`] as const;
};

export const getAuthGetLoginMethodQueryOptions = <
  TData = Awaited<ReturnType<typeof authGetLoginMethod>>,
  TError = ErrorType<unknown>,
>(
  mobile: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof authGetLoginMethod>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getAuthGetLoginMethodQueryKey(mobile);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof authGetLoginMethod>>
  > = ({ signal }) => authGetLoginMethod(mobile, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!mobile,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof authGetLoginMethod>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type AuthGetLoginMethodQueryResult = NonNullable<
  Awaited<ReturnType<typeof authGetLoginMethod>>
>;
export type AuthGetLoginMethodQueryError = ErrorType<unknown>;

export function useAuthGetLoginMethod<
  TData = Awaited<ReturnType<typeof authGetLoginMethod>>,
  TError = ErrorType<unknown>,
>(
  mobile: string,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof authGetLoginMethod>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof authGetLoginMethod>>,
          TError,
          Awaited<ReturnType<typeof authGetLoginMethod>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useAuthGetLoginMethod<
  TData = Awaited<ReturnType<typeof authGetLoginMethod>>,
  TError = ErrorType<unknown>,
>(
  mobile: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof authGetLoginMethod>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof authGetLoginMethod>>,
          TError,
          Awaited<ReturnType<typeof authGetLoginMethod>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useAuthGetLoginMethod<
  TData = Awaited<ReturnType<typeof authGetLoginMethod>>,
  TError = ErrorType<unknown>,
>(
  mobile: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof authGetLoginMethod>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary دریافت روش لاگین کاربر
 */

export function useAuthGetLoginMethod<
  TData = Awaited<ReturnType<typeof authGetLoginMethod>>,
  TError = ErrorType<unknown>,
>(
  mobile: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof authGetLoginMethod>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getAuthGetLoginMethodQueryOptions(mobile, options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary ارسال کد یکبار مصرف
 */
export const authSendLoginOTP = (
  authSendLoginOTPBody: AuthSendLoginOTPBody,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  const formData = new FormData();
  if (authSendLoginOTPBody.secretKey !== undefined) {
    formData.append(`secretKey`, authSendLoginOTPBody.secretKey);
  }

  return api<ApiResultOfGetOtpResponse>(
    {
      url: `/api/general/v1/Auth/SendLoginOTP`,
      method: "POST",
      headers: { "Content-Type": "multipart/form-data" },
      data: formData,
      signal,
    },
    options,
  );
};

export const getAuthSendLoginOTPMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof authSendLoginOTP>>,
    TError,
    { data: AuthSendLoginOTPBody },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof authSendLoginOTP>>,
  TError,
  { data: AuthSendLoginOTPBody },
  TContext
> => {
  const mutationKey = ["authSendLoginOTP"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof authSendLoginOTP>>,
    { data: AuthSendLoginOTPBody }
  > = (props) => {
    const { data } = props ?? {};

    return authSendLoginOTP(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type AuthSendLoginOTPMutationResult = NonNullable<
  Awaited<ReturnType<typeof authSendLoginOTP>>
>;
export type AuthSendLoginOTPMutationBody = AuthSendLoginOTPBody;
export type AuthSendLoginOTPMutationError = ErrorType<unknown>;

/**
 * @summary ارسال کد یکبار مصرف
 */
export const useAuthSendLoginOTP = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof authSendLoginOTP>>,
      TError,
      { data: AuthSendLoginOTPBody },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof authSendLoginOTP>>,
  TError,
  { data: AuthSendLoginOTPBody },
  TContext
> => {
  const mutationOptions = getAuthSendLoginOTPMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary ورود به سیستم با کد یکبار مصرف
 */
export const authLoginByOTP = (
  authLoginByOTPBody: AuthLoginByOTPBody,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  const formData = new FormData();
  if (authLoginByOTPBody.secretKey !== undefined) {
    formData.append(`secretKey`, authLoginByOTPBody.secretKey);
  }
  if (authLoginByOTPBody.otp !== undefined && authLoginByOTPBody.otp !== null) {
    formData.append(`otp`, authLoginByOTPBody.otp);
  }

  return api<ApiResultOfBoolean>(
    {
      url: `/api/general/v1/Auth/LoginByOTP`,
      method: "POST",
      headers: { "Content-Type": "multipart/form-data" },
      data: formData,
      signal,
    },
    options,
  );
};

export const getAuthLoginByOTPMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof authLoginByOTP>>,
    TError,
    { data: AuthLoginByOTPBody },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof authLoginByOTP>>,
  TError,
  { data: AuthLoginByOTPBody },
  TContext
> => {
  const mutationKey = ["authLoginByOTP"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof authLoginByOTP>>,
    { data: AuthLoginByOTPBody }
  > = (props) => {
    const { data } = props ?? {};

    return authLoginByOTP(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type AuthLoginByOTPMutationResult = NonNullable<
  Awaited<ReturnType<typeof authLoginByOTP>>
>;
export type AuthLoginByOTPMutationBody = AuthLoginByOTPBody;
export type AuthLoginByOTPMutationError = ErrorType<unknown>;

/**
 * @summary ورود به سیستم با کد یکبار مصرف
 */
export const useAuthLoginByOTP = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof authLoginByOTP>>,
      TError,
      { data: AuthLoginByOTPBody },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof authLoginByOTP>>,
  TError,
  { data: AuthLoginByOTPBody },
  TContext
> => {
  const mutationOptions = getAuthLoginByOTPMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary ورود به سیستم با کلمه عبور
 */
export const authLoginByPassword = (
  authLoginByPasswordBody: AuthLoginByPasswordBody,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  const formData = new FormData();
  if (authLoginByPasswordBody.secretKey !== undefined) {
    formData.append(`secretKey`, authLoginByPasswordBody.secretKey);
  }
  if (
    authLoginByPasswordBody.password !== undefined &&
    authLoginByPasswordBody.password !== null
  ) {
    formData.append(`password`, authLoginByPasswordBody.password);
  }

  return api<ApiResultOfBoolean>(
    {
      url: `/api/general/v1/Auth/LoginByPassword`,
      method: "POST",
      headers: { "Content-Type": "multipart/form-data" },
      data: formData,
      signal,
    },
    options,
  );
};

export const getAuthLoginByPasswordMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof authLoginByPassword>>,
    TError,
    { data: AuthLoginByPasswordBody },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof authLoginByPassword>>,
  TError,
  { data: AuthLoginByPasswordBody },
  TContext
> => {
  const mutationKey = ["authLoginByPassword"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof authLoginByPassword>>,
    { data: AuthLoginByPasswordBody }
  > = (props) => {
    const { data } = props ?? {};

    return authLoginByPassword(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type AuthLoginByPasswordMutationResult = NonNullable<
  Awaited<ReturnType<typeof authLoginByPassword>>
>;
export type AuthLoginByPasswordMutationBody = AuthLoginByPasswordBody;
export type AuthLoginByPasswordMutationError = ErrorType<unknown>;

/**
 * @summary ورود به سیستم با کلمه عبور
 */
export const useAuthLoginByPassword = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof authLoginByPassword>>,
      TError,
      { data: AuthLoginByPasswordBody },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof authLoginByPassword>>,
  TError,
  { data: AuthLoginByPasswordBody },
  TContext
> => {
  const mutationOptions = getAuthLoginByPasswordMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary خروج از سیستم
 */
export const authLogout = (
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfBoolean>(
    { url: `/api/general/v1/Auth/Logout`, method: "GET", signal },
    options,
  );
};

export const getAuthLogoutQueryKey = () => {
  return [`/api/general/v1/Auth/Logout`] as const;
};

export const getAuthLogoutQueryOptions = <
  TData = Awaited<ReturnType<typeof authLogout>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<
    UseQueryOptions<Awaited<ReturnType<typeof authLogout>>, TError, TData>
  >;
  request?: SecondParameter<typeof api>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getAuthLogoutQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof authLogout>>> = ({
    signal,
  }) => authLogout(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof authLogout>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type AuthLogoutQueryResult = NonNullable<
  Awaited<ReturnType<typeof authLogout>>
>;
export type AuthLogoutQueryError = ErrorType<unknown>;

export function useAuthLogout<
  TData = Awaited<ReturnType<typeof authLogout>>,
  TError = ErrorType<unknown>,
>(
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof authLogout>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof authLogout>>,
          TError,
          Awaited<ReturnType<typeof authLogout>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useAuthLogout<
  TData = Awaited<ReturnType<typeof authLogout>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof authLogout>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof authLogout>>,
          TError,
          Awaited<ReturnType<typeof authLogout>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useAuthLogout<
  TData = Awaited<ReturnType<typeof authLogout>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof authLogout>>, TError, TData>
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary خروج از سیستم
 */

export function useAuthLogout<
  TData = Awaited<ReturnType<typeof authLogout>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof authLogout>>, TError, TData>
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getAuthLogoutQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary ورود به سیستم با کلمه عبور
 */
export const authLogin = (
  authLoginBody: AuthLoginBody,
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  const formData = new FormData();
  if (authLoginBody.username !== undefined && authLoginBody.username !== null) {
    formData.append(`username`, authLoginBody.username);
  }
  if (authLoginBody.password !== undefined && authLoginBody.password !== null) {
    formData.append(`password`, authLoginBody.password);
  }

  return api<ApiResultOfBoolean>(
    {
      url: `/api/backoffice/v1/Auth/LoginByPassword`,
      method: "POST",
      headers: { "Content-Type": "multipart/form-data" },
      data: formData,
      signal,
    },
    options,
  );
};

export const getAuthLoginMutationOptions = <
  TError = ErrorType<unknown>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof authLogin>>,
    TError,
    { data: AuthLoginBody },
    TContext
  >;
  request?: SecondParameter<typeof api>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof authLogin>>,
  TError,
  { data: AuthLoginBody },
  TContext
> => {
  const mutationKey = ["authLogin"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof authLogin>>,
    { data: AuthLoginBody }
  > = (props) => {
    const { data } = props ?? {};

    return authLogin(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type AuthLoginMutationResult = NonNullable<
  Awaited<ReturnType<typeof authLogin>>
>;
export type AuthLoginMutationBody = AuthLoginBody;
export type AuthLoginMutationError = ErrorType<unknown>;

/**
 * @summary ورود به سیستم با کلمه عبور
 */
export const useAuthLogin = <TError = ErrorType<unknown>, TContext = unknown>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof authLogin>>,
      TError,
      { data: AuthLoginBody },
      TContext
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof authLogin>>,
  TError,
  { data: AuthLoginBody },
  TContext
> => {
  const mutationOptions = getAuthLoginMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
/**
 * @summary خروج از سیستم
 */
export const authLogout2 = (
  options?: SecondParameter<typeof api>,
  signal?: AbortSignal,
) => {
  return api<ApiResultOfBoolean>(
    { url: `/api/backoffice/v1/Auth/Logout`, method: "GET", signal },
    options,
  );
};

export const getAuthLogout2QueryKey = () => {
  return [`/api/backoffice/v1/Auth/Logout`] as const;
};

export const getAuthLogout2QueryOptions = <
  TData = Awaited<ReturnType<typeof authLogout2>>,
  TError = ErrorType<unknown>,
>(options?: {
  query?: Partial<
    UseQueryOptions<Awaited<ReturnType<typeof authLogout2>>, TError, TData>
  >;
  request?: SecondParameter<typeof api>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getAuthLogout2QueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof authLogout2>>> = ({
    signal,
  }) => authLogout2(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof authLogout2>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type AuthLogout2QueryResult = NonNullable<
  Awaited<ReturnType<typeof authLogout2>>
>;
export type AuthLogout2QueryError = ErrorType<unknown>;

export function useAuthLogout2<
  TData = Awaited<ReturnType<typeof authLogout2>>,
  TError = ErrorType<unknown>,
>(
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof authLogout2>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof authLogout2>>,
          TError,
          Awaited<ReturnType<typeof authLogout2>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useAuthLogout2<
  TData = Awaited<ReturnType<typeof authLogout2>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof authLogout2>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof authLogout2>>,
          TError,
          Awaited<ReturnType<typeof authLogout2>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useAuthLogout2<
  TData = Awaited<ReturnType<typeof authLogout2>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof authLogout2>>, TError, TData>
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary خروج از سیستم
 */

export function useAuthLogout2<
  TData = Awaited<ReturnType<typeof authLogout2>>,
  TError = ErrorType<unknown>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof authLogout2>>, TError, TData>
    >;
    request?: SecondParameter<typeof api>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getAuthLogout2QueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}
