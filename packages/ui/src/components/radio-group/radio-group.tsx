"use client";

import * as RadioGroupPrimitive from "@radix-ui/react-radio-group";
import { CircleIcon } from "lucide-react";
import * as React from "react";

import { cn } from "@workspace/ui/lib/utils";
import { cva, VariantProps } from "class-variance-authority";

const indicatorClassName = cva("", {
  variants: {
    size: {
      sm: "*:[svg]:size-2.5",
      md: "*:[svg]:size-3",
      lg: "*:[svg]:size-3.5",
    },
  },
  defaultVariants: {
    size: "lg",
  },
});
const radioClassName = cva("", {
  variants: {
    size: {
      sm: "size-4",
      md: "size-[18px]",
      lg: "size-5",
    },
  },
  defaultVariants: {
    size: "lg",
  },
});

function RadioGroup({
  className,
  ...props
}: React.ComponentProps<typeof RadioGroupPrimitive.Root>) {
  return (
    <RadioGroupPrimitive.Root
      data-slot="radio-group"
      className={cn("grid gap-3", className)}
      {...props}
    />
  );
}

function RadioGroupItem({
  className,
  rootClassName,
  label,
  size = "lg",
  ...props
}: React.ComponentProps<typeof RadioGroupPrimitive.Item> & {
  label?: string;
  rootClassName?: string;
} & VariantProps<typeof indicatorClassName>) {
  return (
    <div className={cn("flex items-center justify-end gap-1", rootClassName)}>
      <label
        htmlFor={props?.id}
        className={cn(
          props?.disabled
            ? "text-text-nautral-disable cursor-not-allowed"
            : "text-text-nautral-default cursor-pointer",
          size === "sm" ? "text-[10px]" : "text-xs",
        )}
      >
        {label}
      </label>
      <RadioGroupPrimitive.Item
        data-slot="radio-group-item"
        className={cn(
          "peer group border-icon-primary-default disabled:border-text-nautral-disable text-text-nautral-default focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 aspect-square size-4 shrink-0 cursor-pointer rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",
          "",
          radioClassName({ size }),
          className,
        )}
        {...props}
      >
        <RadioGroupPrimitive.Indicator
          data-slot="radio-group-indicator"
          className={indicatorClassName({
            size,
            className: "relative flex items-center justify-center",
          })}
        >
          <CircleIcon className="stroke-icon-primary-default fill-icon-primary-default group-disabled:fill-text-nautral-disable group-disabled:stroke-text-nautral-disable absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
        </RadioGroupPrimitive.Indicator>
      </RadioGroupPrimitive.Item>
    </div>
  );
}

export { RadioGroup, RadioGroupItem };
