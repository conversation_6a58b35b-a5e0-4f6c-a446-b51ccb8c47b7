import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import {
  RadioGroup,
  RadioGroupItem,
} from "@workspace/ui/components/radio-group";

const meta: Meta<typeof RadioGroupItem> = {
  title: "UI/radio-group",
  component: RadioGroupItem,
  parameters: {
    layout: "centered",
  },
  // tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div className="container max-w-sm p-8" dir="rtl">
        <Story />
      </div>
    ),
  ],
  argTypes: {
    size: {
      control: "radio",
      options: ["lg", "md", "sm"],
    },

    disabled: {
      control: "boolean",
    },
  },
};

export default meta;
type Story = StoryObj<typeof RadioGroupItem>;

export const defaultStory: Story = {
  render: ({ value, ...args }) => (
    <RadioGroup defaultValue="night">
      <RadioGroupItem value="day" id="r1" label="روز" {...args} />
      <RadioGroupItem value="night" id="r2" label="شب" {...args} />
      <RadioGroupItem value="default" id="r3" label="خودکار" {...args} />
    </RadioGroup>
  ),
};
export const disabled: Story = {
  render: ({ value, ...args }) => (
    <RadioGroup defaultValue="night">
      <RadioGroupItem value="day" id="r1" label="روز" {...args} />
      <RadioGroupItem value="night" id="r2" label="شب" {...args} />
      <RadioGroupItem value="default" id="r3" label="خودکار" {...args} />
    </RadioGroup>
  ),
};
