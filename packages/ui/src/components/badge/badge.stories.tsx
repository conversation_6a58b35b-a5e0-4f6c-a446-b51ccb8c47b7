import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Badge } from "@workspace/ui/components/badge";

const meta: Meta<typeof Badge> = {
  title: "UI/badge",
  component: Badge,
  parameters: {
    layout: "centered",
  },
  // tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div className="container max-w-sm p-8">
        <Story />
      </div>
    ),
  ],
  argTypes: {
    state: {
      options: ["active", "default"],
      control: { type: "radio" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof Badge>;

export const Primary: Story = {
  args: {
    children: "همه",
  },
};

// multiple badges beside each other
export const MultipleBadges: Story = {
  render: () => (
    <div className="flex items-center space-x-2">
      <Badge state="active">همه</Badge>
      <Badge>درآمد ثابت</Badge>
      <Badge>سهامی</Badge>
    </div>
  ),
};
