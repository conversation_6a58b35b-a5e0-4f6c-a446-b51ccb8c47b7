import { cn } from "@workspace/ui/lib/utils";
import { useEffect, useRef, useState } from "react";

function SwitchTabs({
  className = undefined,
  tabs,
  activeItemId,
  onChange,
}: {
  className?: string;
  tabs: { id: string | number; label: string }[];
  activeItemId?: string | number;
  onChange?: (id: string | number) => void;
}) {
  const [activeTab, setActiveTab] = useState(activeItemId || tabs[0]!.id);
  const tabRefs = useRef<Record<string | number, HTMLButtonElement | null>>({});
  const activeTabRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const updateActiveTabPosition = () => {
    setTimeout(() => {
      if (
        activeTabRef.current &&
        tabRefs.current[activeTab] &&
        containerRef.current
      ) {
        const activeTabButton = tabRefs.current[activeTab];
        const containerRect = containerRef.current.getBoundingClientRect();
        const activeTabRect = activeTabButton.getBoundingClientRect();

        activeTabRef.current.style.width = `${activeTabRect.width}px`;
        activeTabRef.current.style.left = `${activeTabRect.left - containerRect.left}px`;
      }
    }, 0);
  };

  useEffect(() => {
    updateActiveTabPosition();
    window.addEventListener("resize", updateActiveTabPosition);
    return () => window.removeEventListener("resize", updateActiveTabPosition);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab]);

  useEffect(() => {
    if (activeItemId !== undefined && activeItemId !== activeTab) {
      setActiveTab(activeItemId);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeItemId]);

  const handleTabClick = (id: string | number) => {
    setActiveTab(id);
    if (onChange) {
      onChange(id);
    }
  };

  return (
    <div className={cn("relative", className)} ref={containerRef}>
      <div className="bg-surface-nautral-default-2 flex justify-center rounded-lg p-0.5">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            ref={(el) => {
              tabRefs.current[tab.id] = el;
            }}
            onClick={() => handleTabClick(tab.id)}
            className={cn(
              "relative z-10 h-10 rounded-md px-3 text-sm font-medium transition-colors duration-300 outline-none",
              activeTab === tab.id
                ? "text-text-primary-default"
                : "text-text-nautral-secondary",
            )}
          >
            {tab.label}
          </button>
        ))}
      </div>
      <div
        ref={activeTabRef}
        className="bg-surface-primary border-border-primary-default bg-button-primary-default-2 absolute top-2 h-7 rounded-md border transition-all duration-300 ease-out"
      />
    </div>
  );
}

export default SwitchTabs;
