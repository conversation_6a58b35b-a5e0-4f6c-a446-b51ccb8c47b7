import { fireEvent, render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import * as React from "react";

import { InputNumber } from "../input-number/input-number";

describe("InputNumber Component", () => {
  // Basic rendering tests
  it("renders an input with default props", () => {
    render(<InputNumber placeholder="Enter a number" />);

    const input = screen.getByRole("textbox");
    expect(input).toBeInTheDocument();
    expect(input).toHaveAttribute("type", "text");
  });

  // Formatting tests
  it("formats initial value with commas for thousands", () => {
    render(<InputNumber defaultValue="1000" />);

    const input = screen.getByRole("textbox");
    expect(input).toHaveValue("1,000");
  });

  it("formats decimal values correctly", () => {
    render(<InputNumber defaultValue="1234.56" />);

    const input = screen.getByRole("textbox");
    expect(input).toHaveValue("1,234.56");
  });

  it("handles empty value", () => {
    render(<InputNumber />);

    const input = screen.getByRole("textbox");
    expect(input).toHaveValue("");
  });

  // Input validation tests
  it("allows only numeric input and decimal point", async () => {
    render(<InputNumber placeholder="Enter a number" />);

    const input = screen.getByRole("textbox");
    await userEvent.type(input, "abc123.45def");

    expect(input).toHaveValue("123.45");
  });

  it("ensures only one decimal point exists", async () => {
    render(<InputNumber placeholder="Enter a number" />);

    const input = screen.getByRole("textbox");
    await userEvent.type(input, "123.45.67");

    expect(input).toHaveValue("123.4567");
  });

  // Formatting during input tests
  it("formats numbers with commas as user types", async () => {
    render(<InputNumber placeholder="Enter a number" />);

    const input = screen.getByRole("textbox");
    await userEvent.type(input, "1234567");

    expect(input).toHaveValue("1,234,567");
  });

  it("formats input correctly when typing", async () => {
    render(<InputNumber defaultValue="1000" />);

    const input = screen.getByRole("textbox") as HTMLInputElement;

    // Clear the input and type a new value
    await userEvent.clear(input);
    await userEvent.type(input, "12000");

    // Should be formatted with commas
    expect(input).toHaveValue("12,000");
  });

  // Controlled component tests
  it("works as a controlled component", () => {
    const handleChange = jest.fn();

    const TestComponent = () => {
      const [value, setValue] = React.useState("1000");

      const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        setValue(e.target.value);
        handleChange(e);
      };

      return (
        <InputNumber
          placeholder="Enter a number"
          value={value}
          onChange={onChange}
          data-testid="controlled-input"
        />
      );
    };

    render(<TestComponent />);

    const input = screen.getByTestId("controlled-input");
    expect(input).toHaveValue("1,000");

    fireEvent.change(input, { target: { value: "2000" } });

    // The displayed value should have commas
    expect(input).toHaveValue("2,000");

    // The onChange handler should receive the raw value without commas
    expect(handleChange).toHaveBeenCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({ value: "2000" }),
      }),
    );
  });

  // Edge cases
  it("handles zero correctly", async () => {
    render(<InputNumber placeholder="Enter a number" />);

    const input = screen.getByRole("textbox");
    await userEvent.type(input, "0");

    expect(input).toHaveValue("0");
  });

  it("handles leading decimal point", async () => {
    render(<InputNumber placeholder="Enter a number" />);

    const input = screen.getByRole("textbox");
    await userEvent.type(input, ".5");

    expect(input).toHaveValue(".5");
  });

  it("handles large numbers correctly", () => {
    render(<InputNumber defaultValue="1234567890.12" />);

    const input = screen.getByRole("textbox");
    expect(input).toHaveValue("1,234,567,890.12");
  });

  // Prop passing tests
  it("passes through other props to the underlying Input component", () => {
    render(
      <InputNumber
        placeholder="Enter a number"
        disabled
        aria-label="Amount"
        data-testid="number-input"
      />,
    );

    const input = screen.getByTestId("number-input");
    expect(input).toBeDisabled();
    expect(input).toHaveAttribute("aria-label", "Amount");
  });

  // NEW TESTS FOR commaSeparated PROP
  describe("commaSeparated prop", () => {
    it("disables comma formatting when commaSeparated is false", () => {
      render(<InputNumber defaultValue="1000" commaSeparated={false} />);

      const input = screen.getByRole("textbox");
      expect(input).toHaveValue("1000");
    });

    it("enables comma formatting when commaSeparated is true (default)", () => {
      render(<InputNumber defaultValue="1000" commaSeparated={true} />);

      const input = screen.getByRole("textbox");
      expect(input).toHaveValue("1,000");
    });

    it("formats large numbers without commas when commaSeparated is false", () => {
      render(<InputNumber defaultValue="1234567.89" commaSeparated={false} />);

      const input = screen.getByRole("textbox");
      expect(input).toHaveValue("1234567.89");
    });

    it("does not add commas during typing when commaSeparated is false", async () => {
      render(
        <InputNumber placeholder="Enter a number" commaSeparated={false} />,
      );

      const input = screen.getByRole("textbox");
      await userEvent.type(input, "1234567");

      expect(input).toHaveValue("1234567");
    });

    it("works correctly in controlled mode with commaSeparated false", () => {
      const handleChange = jest.fn();

      const TestComponent = () => {
        const [value, setValue] = React.useState("1000");

        const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
          setValue(e.target.value);
          handleChange(e);
        };

        return (
          <InputNumber
            value={value}
            onChange={onChange}
            commaSeparated={false}
            data-testid="controlled-input-no-comma"
          />
        );
      };

      render(<TestComponent />);

      const input = screen.getByTestId("controlled-input-no-comma");
      expect(input).toHaveValue("1000");

      fireEvent.change(input, { target: { value: "2000" } });

      expect(input).toHaveValue("2000");
      expect(handleChange).toHaveBeenCalledWith(
        expect.objectContaining({
          target: expect.objectContaining({ value: "2000" }),
        }),
      );
    });

    it("still passes raw value to onChange handler when commaSeparated is false", async () => {
      const handleChange = jest.fn();
      render(
        <InputNumber
          onChange={handleChange}
          commaSeparated={false}
          data-testid="no-comma-input"
        />,
      );

      const input = screen.getByTestId("no-comma-input");
      await userEvent.type(input, "1234");

      expect(handleChange).toHaveBeenLastCalledWith(
        expect.objectContaining({
          target: expect.objectContaining({ value: "1234" }),
        }),
      );
    });
  });

  // NEW TESTS FOR ARABIC/PERSIAN NUMERAL CONVERSION
  describe("Arabic/Persian numeral conversion", () => {
    it("converts Arabic numerals to English on all devices", async () => {
      render(<InputNumber placeholder="Enter a number" />);

      const input = screen.getByRole("textbox");

      // Simulate typing Arabic numerals ١٢٣٤ (1234 in Arabic)
      fireEvent.change(input, { target: { value: "١٢٣٤" } });

      expect(input).toHaveValue("1,234");
    });

    it("converts Persian numerals to English on all devices", async () => {
      render(<InputNumber placeholder="Enter a number" />);

      const input = screen.getByRole("textbox");

      // Simulate typing Persian numerals ۱۲۳۴ (1234 in Persian)
      fireEvent.change(input, { target: { value: "۱۲۳۴" } });

      expect(input).toHaveValue("1,234");
    });

    it("converts mixed Arabic and Persian numerals", async () => {
      render(<InputNumber placeholder="Enter a number" />);

      const input = screen.getByRole("textbox");

      // Mix of Arabic ١٢ and Persian ۳۴
      fireEvent.change(input, { target: { value: "١٢۳۴" } });

      expect(input).toHaveValue("1,234");
    });

    it("handles Arabic decimal point conversion", async () => {
      render(<InputNumber placeholder="Enter a number" />);

      const input = screen.getByRole("textbox");

      // Arabic numerals with decimal: ١٢٣.٤٥
      fireEvent.change(input, { target: { value: "١٢٣.٤٥" } });

      expect(input).toHaveValue("123.45");
    });

    it("filters out non-numeric characters while converting numerals", async () => {
      render(<InputNumber placeholder="Enter a number" />);

      const input = screen.getByRole("textbox");

      // Arabic numerals with letters
      fireEvent.change(input, { target: { value: "١٢abc٣٤def" } });

      expect(input).toHaveValue("1,234");
    });

    it("works with both commaSeparated=false and numeral conversion", async () => {
      render(
        <InputNumber placeholder="Enter a number" commaSeparated={false} />,
      );

      const input = screen.getByRole("textbox");

      // Persian numerals
      fireEvent.change(input, { target: { value: "۱۲۳۴۵" } });

      expect(input).toHaveValue("12345"); // No commas
    });

    it("passes converted English numerals to onChange handler without commas", async () => {
      const handleChange = jest.fn();
      render(<InputNumber onChange={handleChange} />);

      const input = screen.getByRole("textbox");

      // Type Arabic numerals
      fireEvent.change(input, { target: { value: "١٢٣٤" } });

      expect(handleChange).toHaveBeenCalledWith(
        expect.objectContaining({
          target: expect.objectContaining({ value: "1234" }), // English numerals without commas
        }),
      );
    });

    it("converts numerals in defaultValue", () => {
      render(<InputNumber defaultValue="١٢٣٤" />);

      const input = screen.getByRole("textbox");
      expect(input).toHaveValue("1,234");
    });

    it("converts numerals in controlled component value", () => {
      const TestComponent = () => {
        const [value, setValue] = React.useState("١٢٣٤");

        return (
          <InputNumber
            value={value}
            onChange={(e) => setValue(e.target.value)}
            data-testid="controlled-input"
          />
        );
      };

      render(<TestComponent />);

      const input = screen.getByTestId("controlled-input");
      expect(input).toHaveValue("1,234");
    });

    it("handles Persian decimal values correctly", async () => {
      render(<InputNumber placeholder="Enter a number" />);

      const input = screen.getByRole("textbox");

      // Persian numerals with decimal: ۱۲۳.۴۵
      fireEvent.change(input, { target: { value: "۱۲۳.۴۵" } });

      expect(input).toHaveValue("123.45");
    });

    it("always passes English numerals to onChange even with commaSeparated=false", async () => {
      const handleChange = jest.fn();
      render(
        <InputNumber
          onChange={handleChange}
          commaSeparated={false}
          data-testid="no-comma-input"
        />,
      );

      const input = screen.getByTestId("no-comma-input");

      // Type Persian numerals
      fireEvent.change(input, { target: { value: "۱۲۳۴" } });

      expect(handleChange).toHaveBeenCalledWith(
        expect.objectContaining({
          target: expect.objectContaining({ value: "1234" }), // Always English without commas
        }),
      );
    });

    it("handles zero in Arabic and Persian", async () => {
      render(<InputNumber placeholder="Enter a number" />);

      const input = screen.getByRole("textbox");

      // Arabic zero: ٠, Persian zero: ۰
      fireEvent.change(input, { target: { value: "٠۰" } });

      expect(input).toHaveValue("00");
    });
  });
});
