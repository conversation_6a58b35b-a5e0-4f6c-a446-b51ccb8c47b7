import * as React from "react";

import { Input } from "@workspace/ui/components/input";
import { cn } from "@workspace/ui/lib/utils";

// Helper function to format number with commas
function formatNumberWithCommas(value: string): string {
  if (!value) return "";

  // Remove any existing commas
  const plainNumber = value.replace(/,/g, "");

  // Handle decimal numbers
  const parts = plainNumber.split(".");
  const integerPart = parts[0] || "";
  const decimalPart = parts.length > 1 ? `.${parts[1]}` : "";

  // Add commas for thousands separator (only to the integer part)
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ",");

  // Combine integer and decimal parts
  return `${formattedInteger}${decimalPart}`;
}

// Helper function to convert Arabic/Persian numerals to English
function convertToEnglishNumerals(input: string): string {
  // Arabic-Indic digits (٠-٩)
  const arabicNumerals = ["٠", "١", "٢", "٣", "٤", "٥", "٦", "٧", "٨", "٩"];
  // Persian/Farsi digits (۰-۹)
  const persianNumerals = ["۰", "۱", "۲", "۳", "۴", "۵", "۶", "۷", "۸", "۹"];
  // English digits (0-9)
  const englishNumerals = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];

  let result: string = input;

  // Convert Arabic numerals to English
  arabicNumerals.forEach((arabic, index) => {
    result = result.replace(
      new RegExp(arabic, "g"),
      englishNumerals[index] ?? "",
    );
  });

  // Convert Persian numerals to English
  persianNumerals.forEach((persian, index) => {
    result = result.replace(
      new RegExp(persian, "g"),
      englishNumerals[index] ?? "",
    );
  });

  return result;
}

type InputNumberProps = Omit<React.ComponentProps<typeof Input>, "type"> & {
  value?: string | number;
  defaultValue?: string | number;
  onChange?: React.ChangeEventHandler<HTMLInputElement>;
  commaSeparated?: boolean;
};

const InputNumber = React.forwardRef<HTMLInputElement, InputNumberProps>(
  (
    {
      className,
      value,
      defaultValue,
      onChange,
      commaSeparated = true,
      ...props
    },
    ref,
  ) => {
    const [internalValue, setInternalValue] = React.useState<string>(() => {
      // Initialize with defaultValue if provided
      if (defaultValue !== undefined) {
        const numStr = String(defaultValue);

        // Convert Arabic/Persian numerals to English
        const convertedStr = convertToEnglishNumerals(numStr);

        // Allow only English numbers (0-9) and decimal point after conversion
        const sanitizedValue = convertedStr.replace(/[^\d.]/g, "");

        // Ensure only one decimal point exists
        const parts = sanitizedValue.split(".");
        const normalizedValue =
          parts.length > 1
            ? `${parts[0]}.${parts.slice(1).join("")}`
            : sanitizedValue;

        return commaSeparated
          ? formatNumberWithCommas(normalizedValue)
          : normalizedValue;
      }
      return "";
    });

    // Track if the component is controlled
    const isControlled = value !== undefined;

    // Format the displayed value with commas (conditionally)
    const displayValue = isControlled
      ? (() => {
          // Convert Arabic/Persian numerals to English first
          const convertedValue = convertToEnglishNumerals(String(value));
          // Then filter to only allow English digits and decimal point
          const cleanValue = convertedValue.replace(/[^\d.]/g, "");
          return commaSeparated
            ? formatNumberWithCommas(cleanValue)
            : cleanValue;
        })()
      : internalValue;

    // Handle input changes
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      let inputValue = e.target.value;

      // Convert Arabic/Persian numerals to English on all devices
      inputValue = convertToEnglishNumerals(inputValue);

      // Allow only English numbers (0-9) and decimal point after conversion
      const rawValue = inputValue.replace(/[^\d.]/g, "");

      // Ensure only one decimal point exists
      const parts = rawValue.split(".");
      const sanitizedValue =
        parts.length > 1 ? `${parts[0]}.${parts.slice(1).join("")}` : rawValue;

      const formattedValue = commaSeparated
        ? formatNumberWithCommas(sanitizedValue)
        : sanitizedValue;

      // For uncontrolled mode, update internal state
      if (!isControlled) {
        setInternalValue(formattedValue);
      }

      // Create a synthetic event with the raw value (without commas)
      if (onChange) {
        const syntheticEvent = {
          ...e,
          target: {
            ...e.target,
            value: sanitizedValue, // Pass the raw value without commas to the onChange handler
          },
        } as React.ChangeEvent<HTMLInputElement>;

        onChange(syntheticEvent);
      }
    };

    return (
      <Input
        ref={ref}
        type="text"
        className={cn(className)}
        value={displayValue}
        onChange={handleChange}
        {...props}
        inputMode={props?.inputMode || "numeric"}
      />
    );
  },
);

InputNumber.displayName = "InputNumber";

export { InputNumber };
