import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import MobilePicker from "@workspace/ui/components/mobile-picker/mobile-picker";
import { useState } from "react";

const meta: Meta<typeof MobilePicker> = {
  title: "UI/MobilePicker",
  component: MobilePicker,
  parameters: {
    layout: "centered",
  },
  // tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div className="container max-w-sm p-8">
        <Story />
      </div>
    ),
  ],
  argTypes: {
    minYear: {
      control: { type: "number" },
      description: "Minimum year for the picker",
    },
    maxYear: {
      control: { type: "number" },
      description: "Maximum year for the picker",
    },
    value: {
      control: { type: "date" },
      description:
        "Date value - serves as initial value for uncontrolled mode or controlled value",
    },
    onChange: {
      action: "onChange",
      description:
        "Callback fired when the date changes. If provided, component is interactive; if not, it's read-only",
    },
  },
};

export default meta;
type Story = StoryObj<typeof MobilePicker>;

// Basic uncontrolled story
export const Uncontrolled: Story = {
  args: {
    minYear: 1300,
    maxYear: 1404,
    // No value or onChange - completely uncontrolled, starts with current date
  },
  render: (args) => {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Uncontrolled Mode</h3>
        <p className="text-sm text-gray-600">
          Component manages its own state starting from current date. No
          onChange handler means changes are not reported to parent.
        </p>
        <MobilePicker {...args} />
      </div>
    );
  },
};

// Uncontrolled with initial value
export const UncontrolledWithInitialValue: Story = {
  args: {
    value: new Date(2023, 5, 15), // June 15, 2023
    minYear: 1300,
    maxYear: 1404,
    // No onChange - makes it uncontrolled but with initial value
  },
  render: (args) => {
    return (
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">
          Uncontrolled with Initial Value
        </h3>
        <p className="text-sm text-gray-600">
          Component starts with a specific date (June 15, 2023) and manages its
          own state. No onChange handler means it's uncontrolled.
        </p>
        <MobilePicker {...args} />
      </div>
    );
  },
};

// Controlled mode story
export const Controlled: Story = {
  args: {
    minYear: 1300,
    maxYear: 1404,
  },
  render: (args) => {
    const ControlledExample = () => {
      const [selectedDate, setSelectedDate] = useState(new Date());

      return (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Controlled Mode</h3>
          <p className="text-sm text-gray-600">
            Parent component controls the state. Current value is displayed
            below.
          </p>
          <div className="rounded-lg bg-gray-100 p-4">
            <p className="text-sm font-medium">Selected Date:</p>
            <p className="text-lg">{selectedDate.toLocaleDateString()}</p>
          </div>
          <MobilePicker
            {...args}
            value={selectedDate}
            onChange={setSelectedDate}
          />
        </div>
      );
    };

    return <ControlledExample />;
  },
};

// Controlled with external controls
export const ControlledWithExternalControls: Story = {
  args: {
    minYear: 1300,
    maxYear: 1404,
  },
  render: (args) => {
    const ControlledWithControlsExample = () => {
      const [selectedDate, setSelectedDate] = useState(new Date());

      const setToday = () => setSelectedDate(new Date());
      const setSpecificDate = () => setSelectedDate(new Date(2023, 0, 1)); // January 1, 2023

      return (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">
            Controlled with External Controls
          </h3>
          <p className="text-sm text-gray-600">
            Demonstrates how external buttons can control the picker value.
          </p>

          <div className="flex gap-2">
            <button
              onClick={setToday}
              className="rounded bg-blue-500 px-3 py-1 text-sm text-white hover:bg-blue-600"
            >
              Set Today
            </button>
            <button
              onClick={setSpecificDate}
              className="rounded bg-green-500 px-3 py-1 text-sm text-white hover:bg-green-600"
            >
              Set Jan 1, 2023
            </button>
          </div>

          <div className="rounded-lg bg-gray-100 p-4">
            <p className="text-sm font-medium">Selected Date:</p>
            <p className="text-lg">{selectedDate.toLocaleDateString()}</p>
            <p className="mt-1 text-xs text-gray-500">
              ISO: {selectedDate.toISOString().split("T")[0]}
            </p>
          </div>

          <MobilePicker
            {...args}
            value={selectedDate}
            onChange={setSelectedDate}
          />
        </div>
      );
    };

    return <ControlledWithControlsExample />;
  },
};
