import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Input } from "@workspace/ui/components/input";
import { SearchIcon, UserIcon } from "lucide-react";

const meta: Meta<typeof Input> = {
  title: "UI/Input/Input",
  component: Input,
  parameters: {
    layout: "centered",
  },
  // tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div className="container max-w-sm p-8">
        <Story />
      </div>
    ),
  ],
  argTypes: {
    variant: {
      options: ["outline", "underline"],
      control: { type: "radio" },
    },
    size: {
      options: ["sm", "md", "lg"],
      control: { type: "radio" },
    },
    disabled: {
      control: { type: "boolean" },
    },
    "aria-invalid": {
      control: { type: "boolean" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof Input>;

export const Default: Story = {
  args: {
    title: "نام کاربری",
    placeholder: "نام کاربری را وارد نمایید",
    required: true,
    helperText: "نام کاربری جهت تمامی سیستم ها استفاده خواهد شد",
    "aria-invalid": false,
  },
};
export const InitialValue: Story = {
  args: {
    placeholder: "عنوان را وارد نمایید",
    title: "عنوان",
    value: "صندوق طلا",
  },
};

export const WithStartAdornment: Story = {
  args: {
    placeholder: "جستجو ...",
    startAdornment: <SearchIcon className="h-4 w-4" />,
  },
};

export const WithEndAdornment: Story = {
  args: {
    placeholder: "نام کاربری",
    endAdornment: <UserIcon className="h-4 w-4" />,
  },
};

export const WithBothAdornments: Story = {
  args: {
    placeholder: "جستجو کاربران ...",
    startAdornment: <SearchIcon className="h-4 w-4" />,
    endAdornment: <UserIcon className="h-4 w-4" />,
  },
};

export const Disabled: Story = {
  args: {
    placeholder: "عنوان فیلد",
    disabled: true,
  },
};

export const title: Story = {
  args: {
    title: "جستجو کاربران ...",
  },
};
export const placeholder: Story = {
  args: {
    placeholder: "جستجو کاربران ...",
  },
};

export const titleAndPlaceholder: Story = {
  args: {
    title: "نام کاربری",
    placeholder: "نام کاربری را وارد نمایید",
  },
};
