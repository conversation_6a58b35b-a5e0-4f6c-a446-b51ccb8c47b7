import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { But<PERSON> } from "@workspace/ui/components/button";
import { ArrowLeft } from "lucide-react";

const meta: Meta<typeof Button> = {
  title: "UI/Button",
  component: Button,
  parameters: {
    layout: "centered",
  },
  // tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div className="container max-w-sm p-8">
        <Story />
      </div>
    ),
  ],
  argTypes: {
    variant: {
      options: ["fill", "outline", "noFrame"],
      control: { type: "radio" },
    },
    color: {
      options: ["default", "warning", "destructive", "success"],
      control: { type: "radio" },
    },
    size: {
      options: ["xs", "sm", "md", "lg"],
      control: { type: "radio" },
    },
    disabled: {
      control: { type: "boolean" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof Button>;

export const Primary: Story = {
  args: {
    children: "ورود",
  },
};

export const StartAdornment: Story = {
  args: {
    children: "بازگشت",
    endAdornment: <ArrowLeft className="h-4 w-4" />,
  },
};

export const EndAdornment: Story = {
  args: {
    children: "بعدی",
    startAdornment: <ArrowLeft className="h-4 w-4 rotate-180" />,
  },
};

export const AsChild: Story = {
  args: {
    asChild: true,
  },
  render: (args) => (
    <Button {...args}>
      <a href="#">سایت سجام</a>
    </Button>
  ),
};
