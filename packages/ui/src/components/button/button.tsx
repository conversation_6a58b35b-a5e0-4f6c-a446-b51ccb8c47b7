import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

import { cn } from "@workspace/ui/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive cursor-pointer disabled:cursor-not-allowed transition-all focus-visible:ring-2 active:ring-2",
  {
    variants: {
      variant: {
        fill: "border border-transparent disabled:bg-button-neutral-disable disabled:text-text-nautral-disable",
        outline:
          "bg-transparent border hover:border-transparent focus-visible:border-transparent disabled:border-border-nautral-disable disabled:text-text-nautral-disable",
        noFrame:
          "bg-transparent border border-transparent hover:border-transparent focus-visible:border-transparent disabled:text-text-nautral-disable",
      },
      color: {
        default: "",
        warning: "",
        destructive: "",
        success: "",
      },
      size: {
        xs: "h-6 rounded-sm text-xs",
        sm: "h-8 rounded-sm text-xs",
        md: "h-10 rounded-sm text-base ",
        lg: "h-12 rounded-sm text-base",
      },
    },
    compoundVariants: [
      // Fill variant with colors
      {
        variant: "fill",
        color: "default",
        class:
          "bg-button-primary-default text-text-on-surface-default hover:bg-button-primary-hover focus-visible:bg-button-primary-hover focus-visible:ring-border-primary-default active:ring-border-primary-default ",
      },
      {
        variant: "fill",
        color: "warning",
        class:
          "bg-button-warning-default text-text-on-surface-default hover:bg-button-warning-hover focus-visible:bg-button-warning-hover focus-visible:ring-border-warning-default active:ring-border-warning-default",
      },
      {
        variant: "fill",
        color: "destructive",
        class:
          "bg-button-error-default text-text-on-surface-default hover:bg-button-error-hover focus-visible:bg-button-error-hover focus-visible:ring-border-error-default active:ring-border-error-default",
      },
      {
        variant: "fill",
        color: "success",
        class:
          "bg-button-success-default text-text-on-surface-default hover:bg-button-success-hover focus-visible:bg-button-success-hover focus-visible:ring-border-success-default active:ring-border-success-default",
      },
      // Outline variant with colors
      {
        variant: "outline",
        color: "default",
        class:
          "text-text-nautral-default hover:text-text-on-surface-default border-border-nautral-default hover:bg-button-primary-hover focus-visible:bg-button-primary-hover focus-visible:ring-border-primary-default active:ring-border-primary-default",
      },
      {
        variant: "outline",
        color: "warning",
        class:
          "text-text-nautral-default hover:text-text-on-surface-default border-border-warning-default hover:bg-button-warning-hover focus-visible:bg-button-warning-hover focus-visible:ring-border-warning-default active:ring-border-warning-default",
      },
      {
        variant: "outline",
        color: "destructive",
        class:
          "text-text-error-default-2 hover:text-text-on-surface-default border-border-error-default-2 hover:bg-button-error-hover focus-visible:bg-button-error-hover focus-visible:ring-border-error-default active:ring-border-error-default",
      },
      {
        variant: "outline",
        color: "success",
        class:
          "text-text-nautral-default hover:text-text-on-surface-default border-border-success-default hover:bg-button-success-hover focus-visible:bg-button-success-hover focus-visible:ring-border-success-default active:ring-border-success-default",
      },
      // NoFrame variant with colors
      {
        variant: "noFrame",
        color: "default",
        class:
          "text-text-nautral-default hover:text-text-on-surface-default hover:bg-button-primary-hover focus-visible:bg-button-primary-hover focus-visible:ring-border-primary-default active:ring-border-primary-default",
      },
      {
        variant: "noFrame",
        color: "warning",
        class:
          "text-text-nautral-default hover:text-text-on-surface-default hover:bg-button-warning-hover focus-visible:bg-button-warning-hover focus-visible:ring-border-warning-default active:ring-border-warning-default",
      },
      {
        variant: "noFrame",
        color: "destructive",
        class:
          "text-text-error-default-2 hover:text-text-on-surface-default hover:bg-button-error-hover focus-visible:bg-button-error-hover focus-visible:ring-border-error-default active:ring-border-error-default",
      },
      {
        variant: "noFrame",
        color: "success",
        class:
          "text-text-nautral-default hover:text-text-on-surface-default hover:bg-button-success-hover focus-visible:bg-button-success-hover focus-visible:ring-border-success-default active:ring-border-success-default",
      },
    ],
    defaultVariants: {
      variant: "fill",
      color: "default",
      size: "md",
    },
  },
);

type ButtonProps = React.ComponentProps<"button"> &
  VariantProps<typeof buttonVariants> &
  (
    | {
        asChild: true;
      }
    | {
        asChild?: false;
        startAdornment?: React.ReactNode;
        endAdornment?: React.ReactNode;
      }
  );

function Button({
  className,
  variant,
  color,
  size,
  asChild = false,
  ...props
}: ButtonProps) {
  const { startAdornment, endAdornment, ...restProps } = !asChild
    ? (props as {
        startAdornment?: React.ReactNode;
        endAdornment?: React.ReactNode;
      } & React.ComponentProps<"button">)
    : { startAdornment: undefined, endAdornment: undefined, ...props };
  const Comp = asChild ? Slot : "button";

  return (
    <Comp
      data-slot="button"
      className={cn(
        buttonVariants({ variant, color, size, className }),
        size === "xs" && (startAdornment ? "ps-2 pe-3" : "px-3"),
        size === "xs" && (endAdornment ? "ps-3 pe-2" : "px-3"),
        size !== "xs" && (startAdornment ? "ps-4 pe-5" : "px-5"),
        size !== "xs" && (endAdornment ? "ps-5 pe-4" : "px-5"),
        "",
      )}
      aria-disabled={restProps.disabled ? true : undefined}
      {...restProps}
    >
      {asChild ? (
        restProps.children
      ) : (
        <>
          {startAdornment && startAdornment}
          {restProps.children}
          {endAdornment && endAdornment}
        </>
      )}
    </Comp>
  );
}

export { Button, buttonVariants };
