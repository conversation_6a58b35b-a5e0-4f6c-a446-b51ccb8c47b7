import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Loading } from "@workspace/ui/components/loading";

const meta: Meta<typeof Loading> = {
  title: "UI/Loading",
  component: Loading,
  parameters: {
    layout: "centered",
  },
  // tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div className="container max-w-sm p-8">
        <Story />
      </div>
    ),
  ],
  argTypes: {
    size: {
      options: ["sm", "md", "lg"],
      control: { type: "radio" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof Loading>;

export const Default: Story = {
  args: {},
};
