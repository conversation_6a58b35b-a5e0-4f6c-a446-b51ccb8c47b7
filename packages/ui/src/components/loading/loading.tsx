import { twMerge } from "tailwind-merge";

const sizes = {
  xs: "h-2 w-2 border-px border-t-px",
  sm: "h-4 w-4 border-2 border-t-2",
  md: "h-8 w-8",
  lg: "h-16 w-16",
};

function Loading({
  className,
  size = "md",
}: {
  className?: string;
  size?: keyof typeof sizes;
}) {
  return (
    <div
      className={twMerge(
        "animate-spin rounded-full border-2 border-t-2 border-t-transparent dark:border-slate-800 dark:border-t-slate-400",
        sizes[size],
        className,
      )}
    />
  );
}

export { Loading };
