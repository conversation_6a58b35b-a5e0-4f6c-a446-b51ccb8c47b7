import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";

import Calendar from "@workspace/ui/assets/icons/calendar.svg";
import { DatePickerInPopOver } from "@workspace/ui/components/date-picker";
import { TDatePickerProps } from "headless-react-datepicker";
import React from "react";
import DatePicker from "./DatePicker";
import DatePickerInInput from "./DatePickerInInput";
import TwoInputsRangePicker, {
  ITwoInputsRangePickerProps,
} from "./TwoInputsRangePicker";
import { IDatePickerProps } from "./types";

const meta: Meta<typeof DatePicker> = {
  title: "UI/DatePicker",
  component: DatePicker,
  parameters: {
    // More on how to position stories at: https://storybook.js.org/docs/configure/story-layout
    layout: "fullscreen",
    deepControls: { enabled: true },
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  // tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div className="container max-w-3xl p-8" dir="rtl">
        <Story />
      </div>
    ),
  ],
  argTypes: {
    config: {
      control: "date",
    },
    // @ts-expect-error [values are typed]
    "config.locale": {
      control: "select",
      options: ["fa-IR", "en-US", "ar-EG", "hi-IN"],
    },
    "config.weekStartsOn": {
      control: "select",
      options: [
        "monday",
        "tuesday",
        "wednesday",
        "thursday",
        "friday",
        "saturday",
        "sunday",
      ],
    },
    "config.weekdayFormat": {
      control: "select",
      options: ["long", "short", "narrow"],
    },
    "config.showOtherDays": {
      control: "boolean",
    },
    "config.otherDaysSelectable": {
      control: "boolean",
    },
    "config.dayFormat": {
      control: "select",
      options: ["numeric", "2-digit"],
    },
    "config.yearRangeFrom": {
      control: "number",
    },
    "config.yearRangeTo": {
      control: "number",
    },
    "config.minDate": {
      control: "date",
    },
    "config.maxDate": {
      control: "date",
    },
    "config.weekends": {
      control: "check",
      options: [
        "saturday",
        "sunday",
        "monday",
        "tuesday",
        "wednesday",
        "thursday",
        "friday",
      ],
    },
    "config.weekendSelectable": {
      control: "boolean",
    },
  },
} satisfies Meta<typeof DatePicker>;

export default meta;
type Story = StoryObj<typeof meta>;

function RenderDatePicker<T extends boolean>(props: IDatePickerProps<T>) {
  return (
    <div className="bg-surface-nautral-default-2 border-border-nautral-default shadow-carts mx-auto w-fit overflow-hidden rounded-lg border p-0">
      <DatePicker {...props} />
    </div>
  );
}

export const SingleDate: Story = {
  render: RenderDatePicker,
  args: {
    isRange: false,
    initialValue: new Date("2024-02-05T00:00:00"),
    calendar: "persian",
    config: {
      locale: "fa-IR",
      weekStartsOn: "saturday",
      showOtherDays: false,
      otherDaysSelectable: false,
      weekdayFormat: "narrow",
      dayFormat: "numeric",
      weekends: ["thursday", "friday"],

      // yearRangeFrom: 1330,
      // yearRangeTo: 1400,
      // minDate: new Date("2024-01-01T00:00:00.000Z"),
      // maxDate: new Date(),
    },
  },
};

export const Range: Story = {
  render: RenderDatePicker,
  args: {
    isRange: true,
    initialValue: [new Date("2024-02-06"), new Date("2024-02-08")],
    calendar: "persian",
    config: {
      locale: "fa-IR",
      weekStartsOn: "saturday",
      showOtherDays: false,
      otherDaysSelectable: false,
      weekdayFormat: "narrow",
      dayFormat: "numeric",
      weekends: ["thursday", "friday"],

      // yearRangeFrom: 1330,
      // yearRangeTo: 1400,
      // minDate: new Date("2024-01-01T00:00:00.000Z"),
      // maxDate: new Date(),
    },
  },
};

function RenderDatePickerControlled(props: ITwoInputsRangePickerProps) {
  return (
    <div className="mx-auto max-w-xl p-16">
      <TwoInputsRangePicker
        {...props}
        onChange={(v) => {
          console.log("onChange", v);
        }}
      />
    </div>
  );
}

export const TwoInputs = {
  render: RenderDatePickerControlled,
  args: {
    initialValue: [
      new Date("2024-02-06T00:00:00"),
      new Date("2024-02-08T00:00:00"),
    ],
    calendar: "persian",
    config: {
      locale: "fa-IR",
      weekStartsOn: "saturday",
      showOtherDays: false,
      otherDaysSelectable: false,
      weekdayFormat: "narrow",
      dayFormat: "numeric",
      weekends: ["thursday", "friday"],

      // yearRangeFrom: 1330,
      // yearRangeTo: 1400,
      // minDate: new Date("2024-01-01T00:00:00.000Z"),
      // maxDate: new Date(),
    },
  },
} satisfies Meta<typeof TwoInputsRangePicker>;

// sample for datepickerinpopover

const RenderDatePickerInPopOver = <T extends boolean>(
  props: IDatePickerProps<T> & {
    children?: React.ReactNode;
    placement?: "bottom-start" | "bottom-end";
    className?: string;
    onCancel?: () => void;
    onConfirm?: (value: TDatePickerProps<T>["initialValue"]) => void;
    closeOnConfirm?: boolean;
  },
) => {
  return (
    <div className="mx-auto max-w-xl p-16">
      <DatePickerInPopOver {...props} hasFooter={false}>
        <Calendar className="size-5 cursor-pointer" />
      </DatePickerInPopOver>
    </div>
  );
};

export const DatePickerInPopOverStory: Story = {
  render: RenderDatePickerInPopOver,
  args: {
    initialValue: new Date("2024-02-05T00:00:00"),
    calendar: "persian",
    config: {
      locale: "fa-IR",
      weekStartsOn: "saturday",
      showOtherDays: false,
      otherDaysSelectable: false,
      weekdayFormat: "narrow",
      dayFormat: "numeric",
      weekends: ["thursday", "friday"],

      // yearRangeFrom: 1330,
      // yearRangeTo: 1400,
      // minDate: new Date("2024-01-01T00:00:00.000Z"),
      // maxDate: new Date(),
    },
  },
};

// DatePickerInInput Stories
function RenderDatePickerInInputBasic() {
  const [value, setValue] = React.useState<Date | undefined>(undefined);

  return (
    <div className="mx-auto max-w-xl p-16">
      <DatePickerInInput
        title="تاریخ تولد"
        placeholder="Choose a date"
        calendar="persian"
        initialValue={value}
        onChange={(date) => {
          setValue(date as Date | undefined);
        }}
        onConfirm={(date) => {
          setValue(date as Date | undefined);
        }}
        onCancel={() => {
          setValue(undefined);
        }}
        config={{
          locale: "fa-IR",
          weekStartsOn: "saturday",
          showOtherDays: false,
          otherDaysSelectable: false,
          weekdayFormat: "narrow",
          dayFormat: "numeric",
          weekends: ["thursday", "friday"],
        }}
      />
    </div>
  );
}

function RenderDatePickerInInputWithValue() {
  const [value, setValue] = React.useState<Date | undefined>(
    new Date("2024-02-05T00:00:00"),
  );

  return (
    <div className="mx-auto max-w-xl p-16">
      <DatePickerInInput
        title="تاریخ صدور"
        placeholder="Choose a date"
        calendar="persian"
        initialValue={value}
        onChange={(date) => {
          setValue(date as Date | undefined);
        }}
        onConfirm={(date) => {
          setValue(date as Date | undefined);
          console.log("Confirmed date:", date);
        }}
        onCancel={() => {
          setValue(undefined);
        }}
        config={{
          locale: "fa-IR",
          weekStartsOn: "saturday",
          showOtherDays: false,
          otherDaysSelectable: false,
          weekdayFormat: "narrow",
          dayFormat: "numeric",
          weekends: ["thursday", "friday"],
        }}
      />
    </div>
  );
}

export const DatePickerInInputBasic: Story = {
  render: RenderDatePickerInInputBasic,
};

export const DatePickerInInputWithInitialValue: Story = {
  render: RenderDatePickerInInputWithValue,
};

// date picker i input but range picker
function RenderDatePickerInInputRange() {
  const [value, setValue] = React.useState<Date[] | undefined>(undefined);

  return (
    <div className="mx-auto max-w-xl p-16">
      <DatePickerInInput
        title="تاریخ صدور"
        placeholder="Choose a date"
        calendar="persian"
        initialValue={value}
        onChange={(date) => {
          setValue(date as Date[] | undefined);
        }}
        onConfirm={(date) => {
          setValue(date as Date[] | undefined);
          console.log("Confirmed date range:", date);
        }}
        onCancel={() => {
          setValue(undefined);
        }}
        config={{
          locale: "fa-IR",
          weekStartsOn: "saturday",
          showOtherDays: false,
          otherDaysSelectable: false,
          weekdayFormat: "narrow",
          dayFormat: "numeric",
          weekends: ["thursday", "friday"],
        }}
        isRange
      />
    </div>
  );
}

export const DatePickerInInputRange: Story = {
  render: RenderDatePickerInInputRange,
};
