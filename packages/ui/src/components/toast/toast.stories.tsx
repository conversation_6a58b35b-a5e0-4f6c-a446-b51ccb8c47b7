import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Button } from "@workspace/ui/components/button";
import { Toaster } from "@workspace/ui/components/toast/toast";
import { toast } from "sonner";

const meta: Meta<typeof Toaster> = {
  title: "UI/Toast",
  component: Toaster,
  parameters: {
    layout: "centered",
  },
  // tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div className="p-8">
        <Toaster />
        <div className="container max-w-2xl">
          <Story />
        </div>
      </div>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof Toaster>;

export const Default: Story = {
  render: () => {
    return (
      <div className="flex flex-col gap-4">
        <Button
          variant="noFrame"
          color="default"
          onClick={() => {
            toast("assss");
          }}
        >
          Default Toast
        </Button>
        <Button
          variant="outline"
          color="destructive"
          onClick={() => {
            toast.error("خطا", {
              description: "خطا در دریافت اطلاعات",
            });
          }}
        >
          Error Toast
        </Button>
        <Button
          variant="outline"
          color="success"
          onClick={() => {
            toast.success("ذخیره موفق", {
              description: "اطلاعات با موفقیت ذخیره شد",
            });
          }}
        >
          success Toast
        </Button>
        <Button
          variant="outline"
          color="default"
          onClick={() => {
            toast.info("این یک پیام اطلاعاتی است", {
              description:
                "این یک پیام اطلاعاتی است که به کاربر نمایش داده می‌شود.",
            });
          }}
        >
          info Toast
        </Button>
        <Button
          variant="outline"
          color="warning"
          onClick={() => {
            toast.warning("منتظر بمانید", {
              description: "لطفا دقایقی دیگر مجدد امتحان نمایید",
            });
          }}
        >
          warning Toast
        </Button>
        <Button
          variant="outline"
          color="destructive"
          onClick={() => {
            toast.error("", {
              description:
                "Lorem ipsum dolor sit amet consectetur adipisicing elit. Ratione nemo facilis, blanditiis, fuga harum ab, quidem veniam vero ducimus et quas. Distinctio corporis maxime debitis voluptatem fugiat, quibusdam perspiciatis sequi.",
            });
          }}
        >
          Long text
        </Button>
      </div>
    );
  },
};
