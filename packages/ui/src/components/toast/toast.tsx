"use client";

import Danger from "@workspace/ui/assets/icons/danger.svg";
import { useIsMobile } from "@workspace/ui/hooks/use-mobile";
import { useTheme } from "next-themes";
import { Toaster as Sonner, toast, ToasterProps } from "sonner";

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme();
  const isMobile = useIsMobile();

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      icons={{
        error: (
          <div className="bg-surface-nautral-active flex size-6 items-center justify-center rounded-sm">
            <Danger className="text-icon-error-default size-[18px]" />
          </div>
        ),
        success: (
          <div className="bg-surface-nautral-active flex size-6 items-center justify-center rounded-sm">
            <Danger className="text-icon-success-default size-[18px]" />
          </div>
        ),
        warning: (
          <div className="bg-surface-nautral-active flex size-6 items-center justify-center rounded-sm">
            <Danger className="text-icon-warning-default size-[18px]" />
          </div>
        ),
        info: (
          <div className="bg-surface-nautral-active flex size-6 items-center justify-center rounded-sm">
            <Danger className="text-icon-primary-default size-[18px]" />
          </div>
        ),
      }}
      toastOptions={{
        classNames: {
          icon: "!size-6 !m-0",
          default: "!items-start !gap-2",
          title: "!text-text-nautral-default !font-bold !text-base",
          description: "!text-text-nautral-default !text-xs",
          error: "!bg-surface-error-default !border-border-error-default",
          success: "!bg-surface-success-default !border-border-success-default",
          warning: "!bg-surface-warning-default !border-border-warning-default",
          info: "!bg-surface-primary-default !border-border-primary-default",
          closeButton:
            "!bg-transparent !border-transparent !top-4  !right-0 !left-[unset] rtl:!left-0 rtl:!right-[unset] !text-text-nautral-default",
        },
        closeButton: props?.closeButton ?? true,
      }}
      position={props?.position || (isMobile ? "top-center" : "top-left")}
      {...props}
    />
  );
};

export { toast, Toaster };
