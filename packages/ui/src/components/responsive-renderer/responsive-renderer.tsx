"use client";

import { useIsMobile } from "@workspace/ui/hooks/use-mobile";
/* eslint-disable react/jsx-no-useless-fragment */
import { JSX } from "react";

/**
 * Shows desktop and mobile components based on screen size,
 * if screen size is unknown (during hydration), shows loading component
 */
function ResponsiveRenderer({
  desktop,
  mobile,
}: {
  desktop: JSX.Element;
  mobile?: JSX.Element;
}): JSX.Element {
  const isMobile = useIsMobile();

  if (mobile && isMobile === true) {
    return mobile;
  }

  return desktop;
}

export default ResponsiveRenderer;
