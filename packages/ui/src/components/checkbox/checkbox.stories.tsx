import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { Checkbox } from "@workspace/ui/components/checkbox";

const meta: Meta<typeof Checkbox> = {
  title: "UI/Checkbox",
  component: Checkbox,
  parameters: {
    layout: "centered",
  },
  // tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div className="container max-w-sm p-8">
        <Story />
      </div>
    ),
  ],
  argTypes: {
    disabled: {
      control: { type: "boolean" },
      description: "Disables the checkbox",
    },
    checked: {
      control: { type: "boolean" },
      description: "Controls the checked state of the checkbox",
    },
  },
};

export default meta;
type Story = StoryObj<typeof Checkbox>;

export const Primary: Story = {
  args: {
    id: "checkbox",
    label: "چک باکس",
  },
};
