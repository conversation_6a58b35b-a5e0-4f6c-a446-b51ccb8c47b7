import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Button } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@workspace/ui/components/dialog";
import {
  DialogProvider,
  useDialog,
} from "@workspace/ui/components/dialog/use-dialog";
import { AlertTriangle, Save, Settings, Trash2, User } from "lucide-react";
import { useState } from "react";

const meta: Meta<typeof Dialog> = {
  title: "UI/Dialog",
  component: Dialog,
  parameters: {
    layout: "centered",
  },
  // tags: ["autodocs"],
  decorators: [
    (Story) => (
      <DialogProvider>
        <div className="container max-w-4xl p-8">
          <Story />
        </div>
      </DialogProvider>
    ),
  ],
};

export default meta;
type Story = StoryObj<typeof Dialog>;

// Basic Dialog using shadcn components
export const BasicDialog: Story = {
  render: () => (
    <Dialog>
      <DialogTrigger asChild>
        <Button>باز کردن</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>عنوان دیالوگ</DialogTitle>
          <DialogDescription>این یک دیالوگ ساده است</DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <p>محتوا</p>
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline">انصراف</Button>
          </DialogClose>
          <Button>ذخیره</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  ),
};

// useDialog Hook Example with shadcn components
function UseDialogExample() {
  const { openDialog, closeDialog, closeAllDialogs, dialogs } = useDialog();

  const openShadcnDialog = () => {
    openDialog({
      content: (
        <div>
          <DialogHeader>
            <DialogTitle>Shadcn Dialog with useDialog</DialogTitle>
            <DialogDescription>
              This dialog uses shadcn components with the useDialog hook.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="text-muted-foreground mb-4 text-sm">
              You can use all the shadcn dialog components (DialogHeader,
              DialogTitle, DialogDescription, DialogFooter, etc.) with the
              useDialog hook.
            </p>
            <div className="rounded-lg border p-4">
              <h4 className="mb-2 font-medium">Benefits:</h4>
              <ul className="space-y-1 text-sm">
                <li>• Full shadcn styling and behavior</li>
                <li>• Proper accessibility features</li>
                <li>• Consistent with your design system</li>
                <li>• Multiple dialog stacking support</li>
              </ul>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => closeDialog("shadcn-dialog")}>Close</Button>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
          </DialogFooter>
        </div>
      ),
      id: "shadcn-dialog",
    });
  };

  const openNestedDialog = () => {
    openDialog({
      content: (
        <div>
          <DialogHeader>
            <DialogTitle>Nested Dialog</DialogTitle>
            <DialogDescription>
              This is a nested dialog using shadcn components. You can open
              multiple dialogs on top of each other.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="text-muted-foreground mb-4 text-sm">
              Each dialog maintains its own state and can be closed
              independently. The z-index is automatically managed to ensure
              proper stacking.
            </p>
          </div>
          <DialogFooter>
            <Button onClick={() => openAnotherDialog()}>
              Open Another Dialog
            </Button>
            <Button variant="outline" onClick={() => closeDialog("nested")}>
              Close This Dialog
            </Button>
          </DialogFooter>
        </div>
      ),
      id: "nested",
    });
  };

  const openAnotherDialog = () => {
    openDialog({
      content: (
        <div>
          <DialogHeader>
            <DialogTitle>Third Shadcn Dialog</DialogTitle>
            <DialogDescription>
              This is the third dialog in the stack! Notice how it appears on
              top.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="text-muted-foreground mb-4 text-sm">
              Each dialog can have different content and they all stack properly
              with automatic z-index management.
            </p>
            <div className="rounded-lg border p-4">
              <h4 className="mb-2 font-medium">Stack Order:</h4>
              <ol className="space-y-1 text-sm">
                <li>1. First dialog (bottom)</li>
                <li>2. Second dialog (middle)</li>
                <li>3. Third dialog (this one - top)</li>
              </ol>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => closeDialog("another")}>
              Close This Dialog
            </Button>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
          </DialogFooter>
        </div>
      ),
      id: "another",
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-4">
        <Button
          onClick={openShadcnDialog}
          startAdornment={<User className="h-4 w-4" />}
        >
          Open Dialog
        </Button>
        <Button
          onClick={openNestedDialog}
          startAdornment={<Settings className="h-4 w-4" />}
        >
          Open Nested Dialog
        </Button>
        {dialogs.length > 0 && (
          <Button variant="outline" onClick={closeAllDialogs}>
            Close All ({dialogs.length})
          </Button>
        )}
      </div>

      {dialogs.length > 0 && (
        <div className="text-muted-foreground text-sm">
          Active dialogs: {dialogs.map((d) => d.id).join(", ")}
        </div>
      )}
    </div>
  );
}

export const Hook: Story = {
  render: () => <UseDialogExample />,
};

// Complex Multi-Dialog Example
function ComplexDialogExample() {
  const { openDialog, closeDialog, closeAllDialogs, dialogs } = useDialog();

  const openConfirmationDialog = () => {
    openDialog({
      content: (
        <div>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="text-text-warning-default h-5 w-5" />
              Confirm Action
            </DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this item? This action cannot be
              undone.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="border-border-warning-default/20 bg-surface-warning-default/5 rounded-lg border p-4">
              <h4 className="text-text-warning-default mb-2 font-medium">
                Warning
              </h4>
              <p className="text-text-warning-default/80 text-sm">
                This will permanently delete the selected item and all
                associated data.
              </p>
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant="outline">Cancel</Button>
            </DialogClose>
            <Button
              onClick={() => {
                closeDialog("confirmation");
                openSuccessDialog();
              }}
              startAdornment={<Trash2 className="h-4 w-4" />}
            >
              Delete
            </Button>
          </DialogFooter>
        </div>
      ),
      id: "confirmation",
    });
  };

  const openSuccessDialog = () => {
    openDialog({
      content: (
        <div>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-green-600">
              <Save className="h-5 w-5" />
              Success
            </DialogTitle>
            <DialogDescription>
              The item has been successfully deleted.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="rounded-lg border border-green-200 bg-green-50 p-4">
              <p className="text-sm text-green-800">
                The operation completed successfully. You can now continue with
                your work.
              </p>
            </div>
          </div>
          <DialogFooter>
            <Button onClick={() => closeDialog("success")}>Continue</Button>
          </DialogFooter>
        </div>
      ),
      id: "success",
    });
  };

  return (
    <div className="space-y-4">
      <div className="text-center">
        <h3 className="mb-2 text-lg font-semibold">
          Complex Multi-Dialog Example
        </h3>
        <p className="text-muted-foreground mb-4 text-sm">
          This example shows how multiple dialogs can work together to create
          complex UI flows.
        </p>
      </div>

      <div className="flex flex-wrap justify-center gap-4">
        <Button
          onClick={openConfirmationDialog}
          startAdornment={<Trash2 className="h-4 w-4" />}
        >
          Delete Item
        </Button>
      </div>

      {dialogs.length > 0 && (
        <div className="space-y-2 text-center">
          <div className="text-muted-foreground text-sm">
            Active dialogs: {dialogs.length}
          </div>
          <Button variant="outline" size="sm" onClick={closeAllDialogs}>
            Close All Dialogs
          </Button>
        </div>
      )}
    </div>
  );
}

export const ComplexMultiDialog: Story = {
  render: () => <ComplexDialogExample />,
};

// Controlled Dialog Example
function ControlledDialogExample() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="space-y-4">
      <div className="text-center">
        <h3 className="mb-2 text-lg font-semibold">Controlled Dialog</h3>
        <p className="text-muted-foreground mb-4 text-sm">
          This dialog is controlled by React state.
        </p>
      </div>

      <div className="flex justify-center">
        <Button onClick={() => setIsOpen(true)}>Open Controlled Dialog</Button>
      </div>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Controlled Dialog</DialogTitle>
            <DialogDescription>
              This dialog's open state is controlled by React state.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="mb-4">
              The dialog is currently {isOpen ? "open" : "closed"}.
            </p>
            <p className="text-muted-foreground text-sm">
              You can control this dialog programmatically using the open and
              onOpenChange props.
            </p>
          </div>
          <DialogFooter>
            <Button onClick={() => setIsOpen(false)}>Close Dialog</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export const ControlledDialog: Story = {
  render: () => <ControlledDialogExample />,
};
