/* All Colors */
:root {
  /* Light */
  /* color */
  --azure-radiance-100: rgb(216 241 255);
  --azure-radiance-200: rgb(186 231 255);
  --azure-radiance-300: rgb(139 218 255);
  --azure-radiance-400: rgb(85 195 255);
  --azure-radiance-50: rgb(238 249 255);
  --azure-radiance-500: rgb(46 166 255);
  --azure-radiance-600: rgb(12 130 249);
  --azure-radiance-700: rgb(16 111 229);
  --azure-radiance-800: rgb(22 77 146);
  --azure-radiance-900: rgb(31 50 83);
  --azure-radiance-950: rgb(21 33 55);
  --azure-radiance-975: rgb(0 10 29);
  --background-nautral-body: var(--gray-scale-80);
  --border-error-default: var(--carnation-500);
  --border-error-default-2: var(--carnation-400);
  --border-nautral-default: var(--gray-scale-100);
  --border-nautral-disable: var(--gray-scale-100);
  --border-nautral-divider: var(--gray-scale-100);
  --border-nautral-hover: var(--gray-scale-400);
  --border-primary-default: var(--azure-radiance-600);
  --border-success-default: var(--jade-600);
  --border-warning-default: var(--saffron-500);
  --button-error-default: var(--carnation-500);
  --button-error-hover: var(--carnation-600);
  --button-error-press: var(--carnation-600);
  --button-neutral-default: var(--gray-scale-950);
  --button-neutral-disable: var(--gray-scale-100);
  --button-neutral-hover: var(--gray-scale-800);
  --button-neutral-press: var(--gray-scale-800);
  --button-primary-default: var(--azure-radiance-600);
  --button-primary-default-2: hsl(42 62% 96%);
  --button-primary-hover: var(--azure-radiance-700);
  --button-primary-press: var(--azure-radiance-700);
  --button-success-default: var(--jade-600);
  --button-success-hover: var(--jade-700);
  --button-success-press: var(--jade-700);
  --button-warning-default: var(--saffron-600);
  --button-warning-hover: var(--saffron-700);
  --button-warning-press: var(--saffron-700);
  --carnation-100: rgb(255 225 225);
  --carnation-200: rgb(255 199 199);
  --carnation-300: rgb(255 160 160);
  --carnation-400: rgb(255 94 94);
  --carnation-50: rgb(255 241 241);
  --carnation-500: rgb(248 59 59);
  --carnation-600: rgb(229 29 29);
  --carnation-700: rgb(193 20 20);
  --carnation-800: rgb(132 24 24);
  --carnation-900: rgb(72 7 7);
  --carnation-950: rgb(28 3 3);
  --gray-scale-100: rgb(189 189 189);
  --gray-scale-20: rgb(252 252 252);
  --gray-scale-200: rgb(133 133 133);
  --gray-scale-300: rgb(103 103 103);
  --gray-scale-40: rgb(245 245 245);
  --gray-scale-400: rgb(84 84 84);
  --gray-scale-500: rgb(61 61 61);
  --gray-scale-60: rgb(244 244 244);
  --gray-scale-600: rgb(52 52 56);
  --gray-scale-700: rgb(40 40 44);
  --gray-scale-80: rgb(239 239 239);
  --gray-scale-800: rgb(37 37 41);
  --gray-scale-900: rgb(31 31 34);
  --gray-scale-950: rgb(22 22 22);
  --icon-error-default: var(--carnation-500);
  --icon-error-default-2: var(--carnation-400);
  --icon-error-hover: var(--carnation-600);
  --icon-neutral-black: var(--gray-scale-800);
  --icon-neutral-default: var(--gray-scale-800);
  --icon-neutral-disable: var(--gray-scale-200);
  --icon-neutral-hover: var(--gray-scale-300);
  --icon-neutral-secondary: var(--gray-scale-300);
  --icon-neutral-white: var(--gray-scale-80);
  --icon-on-surface-default: var(--gray-scale-80);
  --icon-on-surface-neutral: var(--gray-scale-80);
  --icon-primary-default: var(--azure-radiance-600);
  --icon-primary-hover: var(--azure-radiance-700);
  --icon-success-default: var(--jade-600);
  --icon-success-hover: var(--jade-700);
  --icon-warning-default: var(--saffron-500);
  --icon-warning-hover: var(--saffron-600);
  --jade-100: rgb(218 254 239);
  --jade-200: rgb(184 250 221);
  --jade-300: rgb(129 244 195);
  --jade-400: rgb(67 229 160);
  --jade-50: rgb(239 254 247);
  --jade-500: rgb(26 205 129);
  --jade-600: rgb(15 169 104);
  --jade-700: rgb(16 133 84);
  --jade-800: rgb(17 86 58);
  --jade-900: rgb(3 48 31);
  --jade-950: rgb(0 32 32);
  --saffron-100: rgb(252 246 197);
  --saffron-200: rgb(250 235 142);
  --saffron-300: rgb(246 216 78);
  --saffron-400: rgb(241 194 27);
  --saffron-50: rgb(253 251 233);
  --saffron-500: rgb(225 171 17);
  --saffron-600: rgb(194 132 12);
  --saffron-700: rgb(155 94 13);
  --saffron-800: rgb(109 61 22);
  --saffron-900: rgb(64 31 8);
  --saffron-950: rgb(27 13 3);
  --surface-error-default: var(--carnation-50);
  --surface-nautral-active: var(--gray-scale-40);
  --surface-nautral-default-1: var(--gray-scale-20);
  --surface-nautral-default-2: var(--gray-scale-80);
  --surface-nautral-default-3: var(--gray-scale-20);
  --surface-nautral-default-4: var(--gray-scale-40);
  --surface-nautral-disable: var(--gray-scale-200);
  --surface-nautral-modal-cover: rgb(178 178 178 / 0.5);
  --surface-nautral-transparent: rgb(186 186 186 / 0.3);
  --surface-primary-default: var(--azure-radiance-50);
  --surface-success-default: var(--jade-50);
  --surface-warning-default: var(--saffron-50);
  --text-error-default: var(--carnation-500);
  --text-error-default-2: var(--carnation-400);
  --text-nautral-black: var(--gray-scale-800);
  --text-nautral-default: var(--gray-scale-800);
  --text-nautral-disable: var(--gray-scale-200);
  --text-nautral-secondary: var(--gray-scale-300);
  --text-nautral-white: var(--gray-scale-80);
  --text-on-surface-default: var(--gray-scale-80);
  --text-on-surface-neutral: var(--gray-scale-80);
  --text-primary-default: var(--azure-radiance-600);
  --text-primary-hover: var(--azure-radiance-700);
  --text-success-default: var(--jade-600);
  --text-warning-default: var(--saffron-600);
}

.dark {
  /* Dark */
  /* color */
  --azure-radiance-100: rgb(216 241 255);
  --azure-radiance-200: rgb(186 231 255);
  --azure-radiance-300: rgb(139 218 255);
  --azure-radiance-400: rgb(85 195 255);
  --azure-radiance-50: rgb(238 249 255);
  --azure-radiance-500: rgb(46 166 255);
  --azure-radiance-600: rgb(12 130 249);
  --azure-radiance-700: rgb(16 111 229);
  --azure-radiance-800: rgb(22 77 146);
  --azure-radiance-900: rgb(31 50 83);
  --azure-radiance-950: rgb(21 33 55);
  --azure-radiance-975: rgb(0 10 29);
  --background-nautral-body: var(--gray-scale-700);
  --border-error-default: var(--carnation-500);
  --border-error-default-2: var(--carnation-300);
  --border-nautral-default: var(--gray-scale-200);
  --border-nautral-disable: var(--gray-scale-400);
  --border-nautral-divider: var(--gray-scale-300);
  --border-nautral-hover: var(--gray-scale-100);
  --border-primary-default: var(--azure-radiance-600);
  --border-success-default: var(--jade-600);
  --border-warning-default: var(--saffron-400);
  --button-error-default: var(--carnation-600);
  --button-error-hover: var(--carnation-700);
  --button-error-press: var(--carnation-700);
  --button-neutral-default: var(--gray-scale-80);
  --button-neutral-disable: var(--gray-scale-200);
  --button-neutral-hover: var(--gray-scale-100);
  --button-neutral-press: var(--gray-scale-100);
  --button-primary-default: var(--azure-radiance-600);
  --button-primary-default-2: hsl(42 62% 96%);
  --button-primary-hover: var(--azure-radiance-700);
  --button-primary-press: var(--azure-radiance-700);
  --button-success-default: var(--jade-600);
  --button-success-hover: var(--jade-700);
  --button-success-press: var(--jade-700);
  --button-warning-default: var(--saffron-600);
  --button-warning-hover: var(--saffron-700);
  --button-warning-press: var(--saffron-700);
  --carnation-100: rgb(255 225 225);
  --carnation-200: rgb(255 199 199);
  --carnation-300: rgb(255 160 160);
  --carnation-400: rgb(255 94 94);
  --carnation-50: rgb(255 241 241);
  --carnation-500: rgb(248 59 59);
  --carnation-600: rgb(229 29 29);
  --carnation-700: rgb(193 20 20);
  --carnation-800: rgb(132 24 24);
  --carnation-900: rgb(72 7 7);
  --carnation-950: rgb(28 3 3);
  --gray-scale-100: rgb(189 189 189);
  --gray-scale-20: rgb(252 252 252);
  --gray-scale-200: rgb(133 133 133);
  --gray-scale-300: rgb(103 103 103);
  --gray-scale-40: rgb(245 245 245);
  --gray-scale-400: rgb(84 84 84);
  --gray-scale-500: rgb(61 61 61);
  --gray-scale-60: rgb(244 244 244);
  --gray-scale-600: rgb(52 52 56);
  --gray-scale-700: rgb(40 40 44);
  --gray-scale-80: rgb(239 239 239);
  --gray-scale-800: rgb(37 37 41);
  --gray-scale-900: rgb(31 31 34);
  --gray-scale-950: rgb(22 22 22);
  --icon-error-default: var(--carnation-500);
  --icon-error-default-2: var(--carnation-300);
  --icon-error-hover: var(--carnation-600);
  --icon-neutral-black: var(--gray-scale-800);
  --icon-neutral-default: var(--gray-scale-80);
  --icon-neutral-disable: var(--gray-scale-400);
  --icon-neutral-hover: var(--gray-scale-100);
  --icon-neutral-secondary: var(--gray-scale-100);
  --icon-neutral-white: var(--gray-scale-80);
  --icon-on-surface-default: var(--gray-scale-80);
  --icon-on-surface-neutral: var(--gray-scale-800);
  --icon-primary-default: var(--azure-radiance-600);
  --icon-primary-hover: var(--azure-radiance-700);
  --icon-success-default: var(--jade-600);
  --icon-success-hover: var(--jade-700);
  --icon-warning-default: var(--saffron-400);
  --icon-warning-hover: var(--saffron-500);
  --jade-100: rgb(218 254 239);
  --jade-200: rgb(184 250 221);
  --jade-300: rgb(129 244 195);
  --jade-400: rgb(67 229 160);
  --jade-50: rgb(239 254 247);
  --jade-500: rgb(26 205 129);
  --jade-600: rgb(15 169 104);
  --jade-700: rgb(16 133 84);
  --jade-800: rgb(17 86 58);
  --jade-900: rgb(3 48 31);
  --jade-950: rgb(0 32 32);
  --saffron-100: rgb(252 246 197);
  --saffron-200: rgb(250 235 142);
  --saffron-300: rgb(246 216 78);
  --saffron-400: rgb(241 194 27);
  --saffron-50: rgb(253 251 233);
  --saffron-500: rgb(225 171 17);
  --saffron-600: rgb(194 132 12);
  --saffron-700: rgb(155 94 13);
  --saffron-800: rgb(109 61 22);
  --saffron-900: rgb(64 31 8);
  --saffron-950: rgb(27 13 3);
  --surface-error-default: var(--carnation-950);
  --surface-nautral-active: var(--gray-scale-900);
  --surface-nautral-default-1: var(--gray-scale-600);
  --surface-nautral-default-2: var(--gray-scale-700);
  --surface-nautral-default-3: var(--gray-scale-800);
  --surface-nautral-default-4: var(--gray-scale-900);
  --surface-nautral-disable: var(--gray-scale-100);
  --surface-nautral-modal-cover: rgb(22 22 22 / 0.6);
  --surface-nautral-transparent: rgb(61 61 61 / 0.3);
  --surface-primary-default: var(--azure-radiance-975);
  --surface-success-default: var(--jade-950);
  --surface-warning-default: var(--saffron-950);
  --text-error-default: var(--carnation-500);
  --text-error-default-2: var(--carnation-300);
  --text-nautral-black: var(--gray-scale-800);
  --text-nautral-default: var(--gray-scale-80);
  --text-nautral-disable: var(--gray-scale-400);
  --text-nautral-secondary: var(--gray-scale-100);
  --text-nautral-white: var(--gray-scale-80);
  --text-on-surface-default: var(--gray-scale-80);
  --text-on-surface-neutral: var(--gray-scale-800);
  --text-primary-default: var(--azure-radiance-600);
  --text-primary-hover: var(--azure-radiance-700);
  --text-success-default: var(--jade-600);
  --text-warning-default: var(--saffron-500);
}
