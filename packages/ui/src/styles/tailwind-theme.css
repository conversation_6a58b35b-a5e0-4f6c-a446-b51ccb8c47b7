@theme inline {
  /** --------------------------------- colors --------------------------------- */
  --color-*: initial;
  --color-background-nautral-body: var(--background-nautral-body);
  --color-border-error-default: var(--border-error-default);
  --color-border-error-default-2: var(--border-error-default-2);
  --color-border-nautral-default: var(--border-nautral-default);
  --color-border-nautral-disable: var(--border-nautral-disable);
  --color-border-nautral-divider: var(--border-nautral-divider);
  --color-border-nautral-hover: var(--border-nautral-hover);
  --color-border-primary-default: var(--border-primary-default);
  --color-border-success-default: var(--border-success-default);
  --color-border-warning-default: var(--border-warning-default);
  --color-button-error-default: var(--button-error-default);
  --color-button-error-hover: var(--button-error-hover);
  --color-button-error-press: var(--button-error-press);
  --color-button-neutral-default: var(--button-neutral-default);
  --color-button-neutral-disable: var(--button-neutral-disable);
  --color-button-neutral-hover: var(--button-neutral-hover);
  --color-button-neutral-press: var(--button-neutral-press);
  --color-button-primary-default: var(--button-primary-default);
  --color-button-primary-default-2: var(--button-primary-default-2);
  --color-button-primary-hover: var(--button-primary-hover);
  --color-button-primary-press: var(--button-primary-press);
  --color-button-success-default: var(--button-success-default);
  --color-button-success-hover: var(--button-success-hover);
  --color-button-success-press: var(--button-success-press);
  --color-button-warning-default: var(--button-warning-default);
  --color-button-warning-hover: var(--button-warning-hover);
  --color-button-warning-press: var(--button-warning-press);
  --color-icon-error-default: var(--icon-error-default);
  --color-icon-error-default-2: var(--icon-error-default);
  --color-icon-error-hover: var(--icon-error-hover);
  --color-icon-neutral-black: var(--icon-neutral-black);
  --color-icon-neutral-default: var(--icon-neutral-default);
  --color-icon-neutral-disable: var(--icon-neutral-disable);
  --color-icon-neutral-hover: var(--icon-neutral-hover);
  --color-icon-neutral-secondary: var(--icon-neutral-secondary);
  --color-icon-neutral-white: var(--icon-neutral-white);
  --color-icon-on-surface-default: var(--icon-on-surface-default);
  --color-icon-on-surface-neutral: var(--icon-on-surface-neutral);
  --color-icon-primary-default: var(--icon-primary-default);
  --color-icon-primary-hover: var(--icon-primary-hover);
  --color-icon-success-default: var(--icon-success-default);
  --color-icon-success-hover: var(--icon-success-hover);
  --color-icon-warning-default: var(--icon-warning-default);
  --color-icon-warning-hover: var(--icon-warning-hover);
  --color-surface-error-default: var(--surface-error-default);
  --color-surface-nautral-active: var(--surface-nautral-active);
  --color-surface-nautral-default-1: var(--surface-nautral-default-1);
  --color-surface-nautral-default-2: var(--surface-nautral-default-2);
  --color-surface-nautral-default-3: var(--surface-nautral-default-3);
  --color-surface-nautral-default-4: var(--surface-nautral-default-4);
  --color-surface-nautral-disable: var(--surface-nautral-disable);
  --color-surface-nautral-modal-cover: var(--surface-nautral-modal-cover);
  --color-surface-nautral-transparent: var(--surface-nautral-transparent);
  --color-surface-primary-default: var(--surface-primary-default);
  --color-surface-success-default: var(--surface-success-default);
  --color-surface-warning-default: var(--surface-warning-default);
  --color-text-error-default: var(--text-error-default);
  --color-text-error-default-2: var(--text-error-default-2);
  --color-text-nautral-black: var(--text-nautral-black);
  --color-text-nautral-default: var(--text-nautral-default);
  --color-text-nautral-disable: var(--text-nautral-disable);
  --color-text-nautral-secondary: var(--text-nautral-secondary);
  --color-text-nautral-white: var(--text-nautral-white);
  --color-text-on-surface-default: var(--text-on-surface-default);
  --color-text-on-surface-neutral: var(--text-on-surface-neutral);
  --color-text-primary-default: var(--text-primary-default);
  --color-text-primary-hover: var(--text-primary-hover);
  --color-text-success-default: var(--text-success-default);
  --color-text-warning-default: var(--text-warning-default);

  /** --------------------------------- shadows -------------------------------- */
  --shadow-carts: 0px 0px 20px 2px rgba(0, 0, 0, 0.25);
}
