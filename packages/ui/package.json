{"name": "@workspace/ui", "version": "0.0.0", "type": "module", "private": true, "scripts": {"lint": "eslint . --max-warnings 0", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tooltip": "^1.2.6", "@types/lodash": "^4.17.16", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "headless-react-datepicker": "^1.1.9", "lodash": "^4.17.21", "lucide-react": "^0.475.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "react-mobile-picker": "^1.1.2", "react-select": "^5.10.1", "sonner": "^2.0.4", "tailwind-merge": "^3.0.1", "tw-animate-css": "^1.2.4", "vaul": "^1.1.2", "zod": "^3.25.67"}, "devDependencies": {"@jest/globals": "^29.7.0", "@storybook/blocks": "^8.0.0", "@storybook/react": "^8.0.0", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4.0.8", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^15.0.7", "@testing-library/user-event": "^14.6.1", "@turbo/gen": "^2.4.2", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.5.1", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.0.8", "ts-jest": "^29.3.4", "ts-node": "^10.9.2", "typescript": "^5.7.3"}, "exports": {"./globals.css": "./src/styles/globals.css", "./postcss.config": "./postcss.config.mjs", "./lib/*": "./src/lib/*.ts", "./components/*": "./src/components/*.tsx", "./hooks/*": "./src/hooks/*.ts"}}